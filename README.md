# MCX3D Financial Documentation & Valuation System

This project provides a comprehensive system for financial documentation and valuation, including data synchronization from Xero, financial report generation, and company valuation modeling.

## Development Setup

To set up the development environment, you will need Docker and Docker Compose installed.

1.  **Clone the repository:**

    ```bash
    git clone https://github.com/mcx3d/mcx3d-financials.git
    cd mcx3d-financials
    ```

2.  **Build and run the services using Docker Compose:**

    ```bash
    docker-compose up --build
    ```

    This will start the following services:
    *   `web`: The FastAPI application, accessible at `http://localhost:8000`.
    *   `db`: A PostgreSQL database.
    *   `redis`: A Redis instance for caching and task queuing.
    *   `worker`: A Celery worker for background tasks.

## CLI Commands

The project includes a command-line interface (CLI) for various operations. You can run these commands inside the `web` container.

### Generate Financial Reports

The `generate` command group allows you to create financial reports.

*   **Income Statement:**
    ```bash
    docker-compose exec web python -m mcx3d_finance.cli.main generate income-statement --organization-id <org_id> --period <period> --format <pdf|excel|html>
    ```

*   **Balance Sheet:**
    ```bash
    docker-compose exec web python -m mcx3d_finance.cli.main generate balance-sheet --organization-id <org_id> --date <date> --format <pdf|excel|html>
    ```

*   **Cash Flow Statement:**
    ```bash
    docker-compose exec web python -m mcx3d_finance.cli.main generate cash-flow --organization-id <org_id> --period <period> --format <pdf|excel|html>
    ```

### Run Valuation Models

The `valuate` command group allows you to run valuation models.

*   **Discounted Cash Flow (DCF):**
    ```bash
    docker-compose exec web python -m mcx3d_finance.cli.main valuate dcf --config <path_to_config.json>
    ```

*   **Multiples-Based Valuation:**
    ```bash
    docker-compose exec web python -m mcx3d_finance.cli.main valuate multiples --config <path_to_config.json>
    ```

### Synchronize Data

The `sync` command group allows you to synchronize data from external sources.

*   **Sync Xero Data:**
    ```bash
    docker-compose exec web python -m mcx3d_finance.cli.main sync xero --org-id <org_id> [--incremental]