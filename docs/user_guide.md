# User Guide

This guide provides step-by-step instructions for using the key features of the MCX3D Financial Documentation & Valuation System.

## Connecting to Xero

To connect your Xero account, you need to authorize the application to access your Xero data.

1.  **Open your web browser** and navigate to `http://localhost:8000/auth/xero/login`.
2.  You will be **redirected to the Xero login page**. Enter your Xero credentials to log in.
3.  After logging in, you will be asked to **authorize the application** to access your organization's data. Click "Allow access".
4.  Once authorized, you will be **redirected back to the application**, and your Xero account will be connected.

## Generating Financial Reports

You can generate financial reports either through the API or the command-line interface (CLI).

### Using the API

You can use a tool like `curl` or a client like <PERSON><PERSON> to make requests to the API.

*   **Income Statement:**
    ```bash
    curl "http://localhost:8000/reports/income-statement?organization_id=1&period=2023-Q4"
    ```

*   **Balance Sheet:**
    ```bash
    curl "http://localhost:8000/reports/balance-sheet?organization_id=1&date=2023-12-31"
    ```

*   **Cash Flow Statement:**
    ```bash
    curl "http://localhost:8000/reports/cash-flow?organization_id=1&period=2023-Q4"
    ```

### Using the CLI

You can also generate reports using the CLI inside the `web` container.

*   **Income Statement:**
    ```bash
    docker-compose exec web python -m mcx3d_finance.cli.main generate income-statement --organization-id 1 --period 2023-Q4
    ```

*   **Balance Sheet:**
    ```bash
    docker-compose exec web python -m mcx3d_finance.cli.main generate balance-sheet --organization-id 1 --date 2023-12-31
    ```

*   **Cash Flow Statement:**
    ```bash
    docker-compose exec web python -m mcx3d_finance.cli.main generate cash-flow --organization-id 1 --period 2023-Q4
    ```

## Running Valuation Models

You can run valuation models either through the API or the CLI.

### Using the API

*   **Discounted Cash Flow (DCF):**
    ```bash
    curl -X POST "http://localhost:8000/valuation/dcf" \
    -H "Content-Type: application/json" \
    -d '{
      "financial_projections": [
        {"year": 1, "free_cash_flow": 10000},
        {"year": 2, "free_cash_flow": 12000},
        {"year": 3, "free_cash_flow": 15000}
      ],
      "discount_rate": 0.1,
      "terminal_growth_rate": 0.02
    }'
    ```

*   **Multiples-Based Valuation:**
    ```bash
    curl -X POST "http://localhost:8000/valuation/multiples" \
    -H "Content-Type: application/json" \
    -d '{
      "financial_metrics": {
        "revenue": 500000,
        "ebitda": 100000
      },
      "comparable_multiples": {
        "revenue_multiple": 5,
        "ebitda_multiple": 12
      }
    }'
    ```

### Using the CLI

*   **Discounted Cash Flow (DCF):**
    Create a `dcf_config.json` file:
    ```json
    {
      "financial_projections": [
        {"year": 1, "free_cash_flow": 10000},
        {"year": 2, "free_cash_flow": 12000},
        {"year": 3, "free_cash_flow": 15000}
      ],
      "discount_rate": 0.1,
      "terminal_growth_rate": 0.02
    }
    ```
    Then run the command:
    ```bash
    docker-compose exec web python -m mcx3d_finance.cli.main valuate dcf --config dcf_config.json
    ```

*   **Multiples-Based Valuation:**
    Create a `multiples_config.json` file:
    ```json
    {
      "financial_metrics": {
        "revenue": 500000,
        "ebitda": 100000
      },
      "comparable_multiples": {
        "revenue_multiple": 5,
        "ebitda_multiple": 12
      }
    }
    ```
    Then run the command:
    ```bash
    docker-compose exec web python -m mcx3d_finance.cli.main valuate multiples --config multiples_config.json