# API Reference

This document provides a reference for the MCX3D Financial Documentation & Valuation System API.

## Authentication

### `GET /auth/xero/login`

Redirects the user to the Xero authorization URL to begin the OAuth 2.0 flow.

*   **Response:**
    *   `302 Found`: Redirects to the Xero authorization URL.

### `GET /auth/xero/callback`

Handles the callback from Xero after user authorization. It exchanges the authorization code for an access token and stores it.

*   **Query Parameters:**
    *   `code` (string, required): The authorization code from Xero.
    *   `state` (string, required): The state parameter for CSRF protection.
*   **Response:**
    *   `302 Found`: Redirects to the application's frontend.

## Metrics

### `GET /metrics/saas-kpis`

Calculates and returns key SaaS metrics for a given organization and period.

*   **Query Parameters:**
    *   `organization_id` (integer, required): The ID of the organization.
    *   `period` (string, required): The financial period (e.g., "2023-Q4").
*   **Response Body (200 OK):**
    ```json
    {
      "kpis": {
        "mrr": 12000,
        "arr": 144000,
        "customer_churn_rate": 0.05,
        "revenue_churn_rate": 0.02,
        "ltv": 120000,
        "cac": 10000
      }
    }
    ```

## Reports

### `GET /reports/income-statement`

Generates an income statement for a given organization and period.

*   **Query Parameters:**
    *   `organization_id` (integer, required): The ID of the organization.
    *   `period` (string, required): The financial period (e.g., "2023-Q4").
    *   `format` (string, optional, default: `json`): The output format. Can be `json`, `pdf`, `excel`, or `html`.
*   **Response:**
    *   If `format` is `json`, returns a JSON object with the report data.
    *   If `format` is `pdf`, `excel`, or `html`, returns the report as a file download.

### `GET /reports/balance-sheet`

Generates a balance sheet for a given organization and date.

*   **Query Parameters:**
    *   `organization_id` (integer, required): The ID of the organization.
    *   `date` (string, required): The date for the balance sheet (e.g., "2023-12-31").
    *   `format` (string, optional, default: `json`): The output format. Can be `json`, `pdf`, `excel`, or `html`.
*   **Response:**
    *   If `format` is `json`, returns a JSON object with the report data.
    *   If `format` is `pdf`, `excel`, or `html`, returns the report as a file download.

### `GET /reports/cash-flow`

Generates a cash flow statement for a given organization and period.

*   **Query Parameters:**
    *   `organization_id` (integer, required): The ID of the organization.
    *   `period` (string, required): The financial period (e.g., "2023-Q4").
    *   `format` (string, optional, default: `json`): The output format. Can be `json`, `pdf`, `excel`, or `html`.
*   **Response:**
    *   If `format` is `json`, returns a JSON object with the report data.
    *   If `format` is `pdf`, `excel`, or `html`, returns the report as a file download.

## Valuation

### `POST /valuation/dcf`

Runs a Discounted Cash Flow (DCF) valuation based on the provided financial projections.

*   **Request Body:**
    ```json
    {
      "financial_projections": [
        {"year": 1, "free_cash_flow": 10000},
        {"year": 2, "free_cash_flow": 12000},
        {"year": 3, "free_cash_flow": 15000}
      ],
      "discount_rate": 0.1,
      "terminal_growth_rate": 0.02
    }
    ```
*   **Response Body (200 OK):**
    ```json
    {
      "valuation": 150000
    }
    ```

### `POST /valuation/multiples`

Runs a valuation based on market multiples.

*   **Request Body:**
    ```json
    {
      "financial_metrics": {
        "revenue": 500000,
        "ebitda": 100000
      },
      "comparable_multiples": {
        "revenue_multiple": 5,
        "ebitda_multiple": 12
      }
    }
    ```
*   **Response Body (200 OK):**
    ```json
    {
      "valuation": 2500000
    }
    ```

## Webhooks

### `POST /webhooks/xero`

Accepts webhook notifications from Xero.

*   **Headers:**
    *   `xero-signature` (string, required): The signature sent by Xero to verify the payload.
*   **Request Body:**
    *   The raw JSON payload from Xero.
*   **Response:**
    *   `200 OK`: If the webhook is processed successfully.
    *   `401 Unauthorized`: If the signature is invalid.
    *   `500 Internal Server Error`: If the webhook key is not configured.