# MCX3D Financial System - Phase 1 API Documentation

## Overview

Phase 1 of the MCX3D Financial System provides comprehensive financial calculation modules with NASDAQ compliance. This documentation covers all core components and their usage.

---

## Core Components

### 1. Cash Flow Statement Generator

**Module**: `mcx3d_finance.core.financials.cash_flow`

#### CashFlowGenerator Class

Generates NASDAQ-compliant cash flow statements using both indirect and direct methods.

##### Constructor
```python
from mcx3d_finance.core.financials.cash_flow import CashFlowGenerator
from mcx3d_finance.db.session import SessionLocal

db = SessionLocal()
generator = CashFlowGenerator(db)
```

##### Main Methods

###### `generate_cash_flow_statement()`
```python
def generate_cash_flow_statement(
    self,
    organization_id: int,
    from_date: datetime,
    to_date: datetime,
    method: str = "indirect",
    include_comparative: bool = False,
) -> Dict[str, Any]
```

**Parameters:**
- `organization_id`: Database ID of the organization
- `from_date`: Start date for the cash flow period
- `to_date`: End date for the cash flow period
- `method`: "indirect" or "direct" cash flow method
- `include_comparative`: Include prior period comparison

**Returns:** Dictionary containing:
- `header`: Company information and statement metadata
- `operating_activities`: Operating cash flow details
- `investing_activities`: Investment cash flows
- `financing_activities`: Financing cash flows
- `cash_summary`: Beginning/ending cash balances
- `financial_analysis`: Cash flow ratios and quality metrics

**Example Usage:**
```python
from datetime import datetime

result = generator.generate_cash_flow_statement(
    organization_id=1,
    from_date=datetime(2024, 1, 1),
    to_date=datetime(2024, 12, 31),
    method="indirect",
    include_comparative=True
)

print(f"Net Operating Cash Flow: ${result['operating_activities']['net_cash_from_operating']:,.2f}")
print(f"Free Cash Flow: ${result['financial_analysis']['cash_flow_ratios']['free_cash_flow']:,.2f}")
```

---

### 2. DCF Valuation Model

**Module**: `mcx3d_finance.core.valuation.dcf`

#### DCFValuation Class

Performs comprehensive DCF valuations with scenario analysis and Monte Carlo simulation.

##### Constructor
```python
from mcx3d_finance.core.valuation.dcf import DCFValuation

dcf = DCFValuation()
```

##### Main Methods

###### `calculate_dcf_valuation()`
```python
def calculate_dcf_valuation(
    self,
    financial_projections: List[Dict[str, Any]],
    discount_rate: float,
    terminal_growth_rate: float,
    scenarios: List[str] = ["base"],
) -> Dict[str, Any]
```

**Parameters:**
- `financial_projections`: List of yearly financial projections
- `discount_rate`: Discount rate (WACC)
- `terminal_growth_rate`: Long-term growth rate
- `scenarios`: List of scenarios to analyze

**Example Usage:**
```python
projections = [
    {"year": 1, "revenue": 1000000, "free_cash_flow": 300000},
    {"year": 2, "revenue": 1200000, "free_cash_flow": 380000},
    {"year": 3, "revenue": 1440000, "free_cash_flow": 480000},
]

result = dcf.calculate_dcf_valuation(
    financial_projections=projections,
    discount_rate=0.12,
    terminal_growth_rate=0.03,
    scenarios=["base", "upside", "downside"]
)

base_value = result["valuation_results"]["base"]["enterprise_value"]
print(f"Enterprise Value: ${base_value:,.2f}")
```

###### `perform_monte_carlo_simulation()`
```python
def perform_monte_carlo_simulation(
    self,
    projections: List[Dict[str, Any]],
    discount_rate: float,
    terminal_growth_rate: float,
    num_simulations: int = 10000,
    volatility_assumptions: Optional[Dict[str, float]] = None,
) -> Dict[str, Any]
```

**Example Usage:**
```python
monte_carlo = dcf.perform_monte_carlo_simulation(
    projections=projections,
    discount_rate=0.12,
    terminal_growth_rate=0.03,
    num_simulations=10000
)

print(f"Mean Valuation: ${monte_carlo['mean_valuation']:,.2f}")
print(f"90% Confidence Interval: ${monte_carlo['percentiles']['p5']:,.2f} - ${monte_carlo['percentiles']['p95']:,.2f}")
```

###### `calculate_wacc()`
```python
def calculate_wacc(
    self,
    market_value_equity: float,
    market_value_debt: float,
    cost_of_equity: float,
    cost_of_debt: float,
    tax_rate: float = 0.25,
) -> float
```

**Example Usage:**
```python
wacc = dcf.calculate_wacc(
    market_value_equity=50000000,
    market_value_debt=10000000,
    cost_of_equity=0.14,
    cost_of_debt=0.06,
    tax_rate=0.25
)

print(f"WACC: {wacc:.2%}")
```

---

### 3. Multiples Valuation

**Module**: `mcx3d_finance.core.valuation.multiples`

#### MultiplesValuation Class

Performs comprehensive multiples-based valuation with industry benchmarking.

##### Constructor
```python
from mcx3d_finance.core.valuation.multiples import MultiplesValuation

multiples = MultiplesValuation()
```

##### Main Methods

###### `calculate_comprehensive_multiples_valuation()`
```python
def calculate_comprehensive_multiples_valuation(
    self,
    target_metrics: Dict[str, float],
    comparable_companies: List[Dict[str, Any]],
    valuation_multiples: Optional[List[str]] = None,
    weights: Optional[Dict[str, float]] = None,
) -> Dict[str, Any]
```

**Example Usage:**
```python
target_metrics = {
    "revenue": 10000000,
    "ebitda": 3000000,
    "net_income": 1500000,
    "free_cash_flow": 2000000,
}

comparable_companies = [
    {"company": "Comp A", "ev_revenue": 4.5, "ev_ebitda": 15.0, "pe_ratio": 25.0},
    {"company": "Comp B", "ev_revenue": 5.2, "ev_ebitda": 18.0, "pe_ratio": 30.0},
    {"company": "Comp C", "ev_revenue": 3.8, "ev_ebitda": 12.0, "pe_ratio": 22.0},
]

result = multiples.calculate_comprehensive_multiples_valuation(
    target_metrics=target_metrics,
    comparable_companies=comparable_companies,
    valuation_multiples=["ev_revenue", "ev_ebitda", "pe_ratio"]
)

weighted_value = result["weighted_valuation"]["weighted_valuation"]
print(f"Multiples-Based Valuation: ${weighted_value:,.2f}")
```

---

### 4. SaaS KPIs Calculator

**Module**: `mcx3d_finance.core.metrics.saas_kpis`

#### SaaSKPICalculator Class

Calculates comprehensive SaaS-specific financial KPIs and health scores.

##### Constructor
```python
from mcx3d_finance.core.metrics.saas_kpis import SaaSKPICalculator

calculator = SaaSKPICalculator(db_session)
```

##### Main Methods

###### `calculate_comprehensive_kpis()`
```python
def calculate_comprehensive_kpis(
    self, 
    organization_id: int, 
    period_start: datetime, 
    period_end: datetime
) -> Dict[str, Any]
```

**Example Usage:**
```python
from datetime import datetime

result = calculator.calculate_comprehensive_kpis(
    organization_id=1,
    period_start=datetime(2024, 1, 1),
    period_end=datetime(2024, 12, 31)
)

kpis = result["kpis"]
print(f"MRR: ${kpis['revenue_metrics']['monthly_recurring_revenue']:,.2f}")
print(f"ARR: ${kpis['revenue_metrics']['annual_recurring_revenue']:,.2f}")
print(f"LTV/CAC Ratio: {kpis['customer_metrics']['ltv_cac_ratio']:.1f}x")
print(f"Health Score: {kpis['health_score']['overall_score']:.1f}/100 ({kpis['health_score']['health_grade']})")
```

---

## Configuration Options

### Environment Variables

```bash
# Database Configuration
DATABASE_URL=postgresql://user:password@localhost/mcx3d_finance

# Redis Configuration (for caching)
REDIS_URL=redis://localhost:6379

# Logging Level
LOG_LEVEL=INFO

# Default Financial Assumptions
DEFAULT_RISK_FREE_RATE=0.045
DEFAULT_MARKET_RISK_PREMIUM=0.065
DEFAULT_TAX_RATE=0.25
```

### Configuration File

Create `config/financial_settings.yaml`:

```yaml
financial_assumptions:
  risk_free_rate: 0.045
  market_risk_premium: 0.065
  default_tax_rate: 0.25
  default_terminal_growth: 0.025

cash_flow_settings:
  default_method: "indirect"
  include_comparative: true
  currency: "USD"
  amounts_in: "thousands"

dcf_settings:
  default_projection_years: 5
  monte_carlo_simulations: 10000
  sensitivity_ranges:
    discount_rate: [-0.02, 0.02]
    terminal_growth: [-0.01, 0.01]

saas_benchmarks:
  excellent_growth_rate: 25
  good_growth_rate: 15
  excellent_churn_rate: 2
  good_churn_rate: 5
```

---

## Error Handling

All components implement comprehensive error handling:

```python
try:
    result = generator.generate_cash_flow_statement(
        organization_id=1,
        from_date=datetime(2024, 1, 1),
        to_date=datetime(2024, 12, 31)
    )
except ValueError as e:
    print(f"Invalid input: {e}")
except Exception as e:
    print(f"Calculation error: {e}")
```

---

## Performance Considerations

### Caching
- Financial calculations are cached for 1 hour by default
- Use Redis for distributed caching in production

### Database Optimization
- Ensure proper indexing on date and organization_id columns
- Use connection pooling for high-volume operations

### Memory Management
- Large datasets are processed in chunks
- Decimal precision is used for all monetary calculations

---

## Next Steps

This completes the Phase 1 API documentation. For implementation examples and advanced usage patterns, see the `examples/` directory.
