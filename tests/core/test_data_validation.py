"""
Comprehensive tests for the data validation module.
Tests all validation components including financial integrity, business rules,
cross-reference validation, regulatory compliance, and data freshness.
"""

import pytest
import sys
import os
from datetime import datetime, timedelta
from decimal import Decimal
from unittest.mock import Mock, patch

# Add project root to path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')))

from mcx3d_finance.core.data_validation import (
    DataValidationEngine,
    FinancialIntegrityValidator,
    BusinessRuleValidator,
    CrossReferenceValidator,
    RegulatoryComplianceValidator,
    DataFreshnessValidator,
    ValidationSeverity,
    ValidationCategory,
    ValidationResult,
    ValidationReport
)


class TestDataValidationEngine:
    """Test the main data validation engine."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.engine = DataValidationEngine()
        self.sample_data = self._create_sample_data()
    
    def _create_sample_data(self):
        """Create sample data for testing."""
        return {
            "balance_sheet": {
                "report_date": "2024-12-31T00:00:00Z",
                "assets": {
                    "current_assets": {
                        "cash_and_cash_equivalents": 100000,
                        "accounts_receivable": 50000
                    },
                    "non_current_assets": {
                        "property_plant_equipment": 200000
                    },
                    "total_assets": 350000
                },
                "liabilities": {
                    "current_liabilities": {
                        "accounts_payable": 30000
                    },
                    "non_current_liabilities": {
                        "long_term_debt": 120000
                    },
                    "total_liabilities": 150000
                },
                "equity": {
                    "stockholders_equity": {
                        "retained_earnings": 200000
                    },
                    "total_equity": 200000
                }
            },
            "income_statement": {
                "report_date": "2024-12-31T00:00:00Z",
                "revenue": {
                    "total_revenue": 500000
                },
                "expenses": {
                    "total_expenses": 400000
                },
                "net_income": 100000
            },
            "cash_flow": {
                "report_date": "2024-12-31T00:00:00Z",
                "operating_activities": {
                    "net_income": 100000,
                    "net_cash_from_operating": 120000
                },
                "investing_activities": {
                    "net_cash_from_investing": -50000
                },
                "financing_activities": {
                    "net_cash_from_financing": -30000
                },
                "cash_summary": {
                    "net_change_in_cash": 40000
                }
            },
            "transactions": [
                {
                    "id": "txn_1",
                    "date": "2024-12-30",
                    "amount": 1000,
                    "account_id": "acc_1",
                    "description": "Test transaction",
                    "created_date": "2024-12-30T10:00:00Z",
                    "created_by": "system",
                    "last_modified_date": "2024-12-30T10:00:00Z"
                }
            ],
            "accounts": [
                {"id": "acc_1", "name": "Cash Account", "type": "asset"}
            ],
            "invoices": [
                {"id": "inv_1", "total": 1000, "contact_id": "contact_1"}
            ],
            "contacts": [
                {"id": "contact_1", "name": "Test Customer"}
            ],
            "payments": [
                {"id": "pay_1", "invoice_id": "inv_1", "amount": 1000}
            ],
            "metadata": {
                "last_sync_date": "2024-12-31T12:00:00Z",
                "sync_status": "completed",
                "data_extraction_date": "2024-12-31T11:00:00Z"
            }
        }
    
    def test_validate_data_success(self):
        """Test successful data validation."""
        report = self.engine.validate_data(1, self.sample_data)
        
        assert isinstance(report, ValidationReport)
        assert report.organization_id == 1
        assert report.total_checks > 0
        assert report.success_rate >= 0
        assert len(report.results) > 0
        assert isinstance(report.summary, dict)
    
    def test_validate_data_with_errors(self):
        """Test data validation with errors."""
        # Create data with balance sheet equation error
        bad_data = self.sample_data.copy()
        bad_data["balance_sheet"]["assets"]["total_assets"] = 1000000  # Unbalanced
        
        report = self.engine.validate_data(1, bad_data)
        
        assert report.failed_checks > 0
        assert any(not r.passed for r in report.results)
    
    def test_add_custom_validator(self):
        """Test adding a custom validator."""
        class CustomValidator:
            def validate(self, data):
                return [ValidationResult(
                    check_name="custom_check",
                    category=ValidationCategory.DATA_QUALITY,
                    severity=ValidationSeverity.INFO,
                    passed=True,
                    message="Custom validation passed"
                )]
        
        self.engine.add_validator(ValidationCategory.DATA_QUALITY, CustomValidator())
        report = self.engine.validate_data(1, self.sample_data)
        
        assert any(r.check_name == "custom_check" for r in report.results)


class TestFinancialIntegrityValidator:
    """Test financial integrity validation."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.validator = FinancialIntegrityValidator()
    
    def test_balance_sheet_equation_valid(self):
        """Test valid balance sheet equation."""
        data = {
            "balance_sheet": {
                "assets": {"total_assets": 100000},
                "liabilities": {"total_liabilities": 60000},
                "equity": {"total_equity": 40000}
            }
        }
        
        results = self.validator.validate(data)
        balance_sheet_result = next(r for r in results if r.check_name == "balance_sheet_equation")
        
        assert balance_sheet_result.passed
        assert balance_sheet_result.severity == ValidationSeverity.INFO
    
    def test_balance_sheet_equation_invalid(self):
        """Test invalid balance sheet equation."""
        data = {
            "balance_sheet": {
                "assets": {"total_assets": 100000},
                "liabilities": {"total_liabilities": 60000},
                "equity": {"total_equity": 30000}  # Should be 40000
            }
        }
        
        results = self.validator.validate(data)
        balance_sheet_result = next(r for r in results if r.check_name == "balance_sheet_equation")
        
        assert not balance_sheet_result.passed
        assert balance_sheet_result.severity == ValidationSeverity.ERROR
        assert "10000" in balance_sheet_result.message  # Difference amount
    
    def test_cash_flow_consistency_valid(self):
        """Test valid cash flow consistency."""
        data = {
            "cash_flow": {
                "operating_activities": {"net_cash_from_operating": 50000},
                "investing_activities": {"net_cash_from_investing": -20000},
                "financing_activities": {"net_cash_from_financing": -10000},
                "cash_summary": {"net_change_in_cash": 20000}
            }
        }
        
        results = self.validator.validate(data)
        cash_flow_result = next(r for r in results if r.check_name == "cash_flow_consistency")
        
        assert cash_flow_result.passed
        assert cash_flow_result.severity == ValidationSeverity.INFO
    
    def test_income_statement_calculation_valid(self):
        """Test valid income statement calculation."""
        data = {
            "income_statement": {
                "revenue": {"total_revenue": 100000},
                "expenses": {"total_expenses": 70000},
                "net_income": 30000
            }
        }
        
        results = self.validator.validate(data)
        income_result = next(r for r in results if r.check_name == "income_statement_calculation")
        
        assert income_result.passed
        assert income_result.severity == ValidationSeverity.INFO


class TestBusinessRuleValidator:
    """Test business rule validation."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.validator = BusinessRuleValidator()
    
    def test_negative_revenue_detection(self):
        """Test detection of negative revenue."""
        data = {
            "income_statement": {
                "revenue": {"total_revenue": -50000}  # Negative revenue
            }
        }
        
        results = self.validator.validate(data)
        revenue_result = next(r for r in results if r.check_name == "negative_revenue")
        
        assert not revenue_result.passed
        assert revenue_result.severity == ValidationSeverity.ERROR
        assert "Negative revenue detected" in revenue_result.message
    
    def test_negative_cash_detection(self):
        """Test detection of negative cash balance."""
        data = {
            "balance_sheet": {
                "assets": {
                    "current_assets": {
                        "cash_and_cash_equivalents": -10000  # Negative cash
                    }
                }
            }
        }
        
        results = self.validator.validate(data)
        cash_result = next(r for r in results if r.check_name == "negative_cash")
        
        assert not cash_result.passed
        assert cash_result.severity == ValidationSeverity.WARNING
    
    def test_duplicate_transaction_detection(self):
        """Test detection of duplicate transactions."""
        data = {
            "transactions": [
                {
                    "date": "2024-01-01",
                    "amount": 1000,
                    "account_id": "acc_1",
                    "description": "Test transaction"
                },
                {
                    "date": "2024-01-01",
                    "amount": 1000,
                    "account_id": "acc_1",
                    "description": "Test transaction"  # Duplicate
                }
            ]
        }
        
        results = self.validator.validate(data)
        duplicate_result = next(r for r in results if r.check_name == "duplicate_transactions")
        
        assert not duplicate_result.passed
        assert duplicate_result.severity == ValidationSeverity.WARNING


class TestCrossReferenceValidator:
    """Test cross-reference validation."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.validator = CrossReferenceValidator()
    
    def test_invoice_payment_consistency(self):
        """Test invoice-payment consistency validation."""
        data = {
            "invoices": [
                {"id": "inv_1", "total": 1000}
            ],
            "payments": [
                {"invoice_id": "inv_1", "amount": 1000}
            ]
        }
        
        results = self.validator.validate(data)
        consistency_result = next(r for r in results if r.check_name == "invoice_payment_consistency")
        
        assert consistency_result.passed
        assert consistency_result.severity == ValidationSeverity.INFO
    
    def test_orphaned_payments_detection(self):
        """Test detection of orphaned payments."""
        data = {
            "invoices": [
                {"id": "inv_1", "total": 1000}
            ],
            "payments": [
                {"invoice_id": "inv_2", "amount": 500}  # Orphaned payment
            ]
        }
        
        results = self.validator.validate(data)
        orphaned_result = next(r for r in results if r.check_name == "orphaned_payments")
        
        assert not orphaned_result.passed
        assert orphaned_result.severity == ValidationSeverity.WARNING


class TestDataFreshnessValidator:
    """Test data freshness validation."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.validator = DataFreshnessValidator(max_age_hours=24)
    
    def test_fresh_data_validation(self):
        """Test validation of fresh data."""
        current_time = datetime.utcnow()
        data = {
            "metadata": {
                "last_sync_date": current_time.isoformat(),
                "sync_status": "completed"
            }
        }
        
        results = self.validator.validate(data)
        freshness_result = next(r for r in results if r.check_name == "data_freshness_check")
        
        assert freshness_result.passed
        assert freshness_result.severity == ValidationSeverity.INFO
    
    def test_stale_data_detection(self):
        """Test detection of stale data."""
        old_time = datetime.utcnow() - timedelta(hours=48)
        data = {
            "metadata": {
                "last_sync_date": old_time.isoformat(),
                "sync_status": "completed"
            }
        }
        
        results = self.validator.validate(data)
        staleness_result = next(r for r in results if r.check_name == "stale_data_detection")
        
        assert not staleness_result.passed
        assert staleness_result.severity == ValidationSeverity.WARNING


if __name__ == "__main__":
    pytest.main([__file__])
