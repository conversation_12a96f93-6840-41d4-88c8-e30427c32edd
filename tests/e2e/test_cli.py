import os
import subprocess
import pytest


@pytest.mark.e2e
def test_generate_income_statement_success():
    """
    Tests successful generation of an income statement report via the CLI.
    """
    output_path = "income_statement.xlsx"
    if os.path.exists(output_path):
        os.remove(output_path)

    command = [
        "mcx3d-finance",
        "generate",
        "income-statement",
        "--organization-id",
        "test-org",
        "--period",
        "2023-Q4",
        "--format",
        "excel",
    ]
    result = subprocess.run(command, capture_output=True, text=True)

    assert result.returncode == 0
    assert os.path.exists(output_path)
    # TODO: Add more specific content validation for the generated excel file
    os.remove(output_path)


@pytest.mark.e2e
def test_generate_income_statement_missing_option():
    """
    Tests that the CLI returns an error when a required option is missing.
    """
    command = [
        "mcx3d-finance",
        "generate",
        "income-statement",
        "--organization-id",
        "test-org",
        # Missing --period
    ]
    result = subprocess.run(command, capture_output=True, text=True)

    assert result.returncode != 0
    assert "Error: Missing option '--period'" in result.stderr
