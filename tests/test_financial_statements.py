"""
Comprehensive tests for financial statement generation.
"""

import pytest
from datetime import datetime, timedelta
from mcx3d_finance.core.financials.balance_sheet import BalanceSheetGenerator
from mcx3d_finance.core.financials.income_statement import IncomeStatementGenerator
from mcx3d_finance.core.financials.cash_flow import CashFlowGenerator

class TestFinancialStatements:
    """Test financial statement generation."""
    
    def test_balance_sheet_generation(self, db_session, sample_organization):
        """Test balance sheet generation with NASDAQ compliance."""
        generator = BalanceSheetGenerator(db_session)
        
        balance_sheet = generator.generate_balance_sheet(
            sample_organization.id,
            datetime.utcnow(),
            include_comparative=True
        )
        
        # Verify structure
        assert 'header' in balance_sheet
        assert 'assets' in balance_sheet
        assert 'liabilities_and_equity' in balance_sheet
        assert 'financial_analysis' in balance_sheet
        
        # Verify NASDAQ compliance
        assert balance_sheet['header']['statement_title'] == 'CONSOLIDATED BALANCE SHEETS'
        assert 'amounts_in' in balance_sheet['header']
        assert 'currency' in balance_sheet['header']
        
        # Verify balance equation
        total_assets = balance_sheet['assets']['total_assets']['current']
        total_liab_equity = balance_sheet['liabilities_and_equity']['total_liabilities_and_equity']['current']
        assert abs(total_assets - total_liab_equity) < 0.01  # Allow for rounding