#!/usr/bin/env python3
"""
Test script for the Cash Flow Generator to verify it works correctly.
"""

import sys
import os
from datetime import datetime, timedelta
from decimal import Decimal

# Add the project root to the Python path
sys.path.insert(0, os.path.abspath('.'))

from mcx3d_finance.core.financials.cash_flow import CashFlowGenerator
from mcx3d_finance.db.session import SessionLocal
from mcx3d_finance.db.models import Organization, Account, Transaction

def create_test_data():
    """Create test data for cash flow generation."""
    db = SessionLocal()
    
    try:
        # Create test organization
        org = Organization(
            id=1,
            name="Test Company Inc.",
            xero_tenant_id="test-tenant-123"
        )
        db.add(org)
        
        # Create test accounts
        accounts = [
            Account(id="cash-001", name="Cash and Cash Equivalents", type="CASH", status="ACTIVE", organization_id=1),
            Account(id="rev-001", name="Software Revenue", type="REVENUE", status="ACTIVE", organization_id=1),
            Account(id="exp-001", name="Operating Expenses", type="EXPENSE", status="ACTIVE", organization_id=1),
            Account(id="dep-001", name="Depreciation Expense", type="DEPRECIATION", status="ACTIVE", organization_id=1),
            Account(id="cap-001", name="Equipment", type="FIXED_ASSET", status="ACTIVE", organization_id=1),
        ]
        
        for account in accounts:
            db.add(account)
        
        # Create test transactions
        base_date = datetime(2024, 1, 1)
        transactions = [
            # Revenue transactions
            Transaction(id="txn-001", type="CREDIT", date=base_date + timedelta(days=1), 
                       amount=50000, account_id="rev-001", organization_id=1),
            Transaction(id="txn-002", type="CREDIT", date=base_date + timedelta(days=15), 
                       amount=75000, account_id="rev-001", organization_id=1),
            Transaction(id="txn-003", type="CREDIT", date=base_date + timedelta(days=30), 
                       amount=60000, account_id="rev-001", organization_id=1),
            
            # Expense transactions
            Transaction(id="txn-004", type="DEBIT", date=base_date + timedelta(days=5), 
                       amount=-25000, account_id="exp-001", organization_id=1),
            Transaction(id="txn-005", type="DEBIT", date=base_date + timedelta(days=20), 
                       amount=-30000, account_id="exp-001", organization_id=1),
            
            # Depreciation
            Transaction(id="txn-006", type="DEBIT", date=base_date + timedelta(days=31), 
                       amount=-5000, account_id="dep-001", organization_id=1),
            
            # Capital expenditure
            Transaction(id="txn-007", type="DEBIT", date=base_date + timedelta(days=10), 
                       amount=-15000, account_id="cap-001", organization_id=1),
            
            # Cash transactions
            Transaction(id="txn-008", type="DEBIT", date=base_date - timedelta(days=1), 
                       amount=100000, account_id="cash-001", organization_id=1),  # Beginning cash
        ]
        
        for transaction in transactions:
            db.add(transaction)
        
        db.commit()
        print("✅ Test data created successfully")
        
    except Exception as e:
        print(f"❌ Error creating test data: {e}")
        db.rollback()
    finally:
        db.close()

def test_cash_flow_generator():
    """Test the cash flow generator with both methods."""
    db = SessionLocal()
    
    try:
        generator = CashFlowGenerator(db)
        
        # Test period
        from_date = datetime(2024, 1, 1)
        to_date = datetime(2024, 1, 31)
        
        print("\n🧪 Testing Indirect Cash Flow Method...")
        indirect_cash_flow = generator.generate_cash_flow_statement(
            organization_id=1,
            from_date=from_date,
            to_date=to_date,
            method="indirect",
            include_comparative=False
        )
        
        print("✅ Indirect method completed successfully")
        print(f"   Net Cash from Operating: ${indirect_cash_flow['operating_activities']['net_cash_from_operating']:,.2f}")
        print(f"   Net Cash from Investing: ${indirect_cash_flow['investing_activities']['net_cash_from_investing']:,.2f}")
        print(f"   Net Cash from Financing: ${indirect_cash_flow['financing_activities']['net_cash_from_financing']:,.2f}")
        print(f"   Net Change in Cash: ${indirect_cash_flow['cash_summary']['net_change_in_cash']:,.2f}")
        
        print("\n🧪 Testing Direct Cash Flow Method...")
        direct_cash_flow = generator.generate_cash_flow_statement(
            organization_id=1,
            from_date=from_date,
            to_date=to_date,
            method="direct",
            include_comparative=False
        )
        
        print("✅ Direct method completed successfully")
        print(f"   Cash Receipts from Customers: ${direct_cash_flow['operating_activities']['cash_receipts_from_customers']:,.2f}")
        print(f"   Net Cash from Operating: ${direct_cash_flow['operating_activities']['net_cash_from_operating']:,.2f}")
        print(f"   Net Change in Cash: ${direct_cash_flow['cash_summary']['net_change_in_cash']:,.2f}")
        
        print("\n📊 Financial Analysis:")
        analysis = indirect_cash_flow.get('financial_analysis', {})
        if analysis:
            print(f"   Free Cash Flow: ${analysis.get('cash_flow_ratios', {}).get('free_cash_flow', 0):,.2f}")
            print(f"   Cash Burn Rate: ${analysis.get('liquidity_analysis', {}).get('cash_burn_rate', 0):,.2f}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing cash flow generator: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        db.close()

def cleanup_test_data():
    """Clean up test data."""
    db = SessionLocal()
    
    try:
        # Delete in reverse order due to foreign key constraints
        db.query(Transaction).filter(Transaction.organization_id == 1).delete()
        db.query(Account).filter(Account.organization_id == 1).delete()
        db.query(Organization).filter(Organization.id == 1).delete()
        db.commit()
        print("🧹 Test data cleaned up successfully")
        
    except Exception as e:
        print(f"❌ Error cleaning up test data: {e}")
        db.rollback()
    finally:
        db.close()

if __name__ == "__main__":
    print("🚀 Starting Cash Flow Generator Test")
    
    # Create test data
    create_test_data()
    
    # Test the generator
    success = test_cash_flow_generator()
    
    # Clean up
    cleanup_test_data()
    
    if success:
        print("\n✅ All tests passed! Cash Flow Generator is working correctly.")
    else:
        print("\n❌ Tests failed. Please check the implementation.")
        sys.exit(1)
