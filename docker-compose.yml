version: '3.8'

services:
  web:
    build: .
    ports:
      - "8000:8000"
    volumes:
      - .:/app
    environment:
      - DATABASE_URL=**********************************/mcx3d_db
      - REDIS_URL=redis://redis:6379/0
    depends_on:
      - db
      - redis

  db:
    image: postgres:13
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data/
    environment:
      - POSTGRES_USER=user
      - POSTGRES_PASSWORD=password
      - POSTGRES_DB=mcx3d_db

  redis:
    image: redis:6.2

  worker:
    build: .
    command: celery -A mcx3d_finance.tasks.celery_app worker --loglevel=info
    volumes:
      - .:/app
    environment:
      - DATABASE_URL=**********************************/mcx3d_db
      - REDIS_URL=redis://redis:6379/0
    depends_on:
      - db
      - redis

volumes:
  postgres_data: