"""
Comprehensive SaaS-specific KPI calculations for financial analysis.
"""

from typing import Dict, Any, List, Optional
from decimal import Decimal, ROUND_HALF_UP
from datetime import datetime, timedelta
import pandas as pd
import logging
from sqlalchemy.orm import Session
from sqlalchemy import func, and_, or_

logger = logging.getLogger(__name__)


class SaaSKPICalculator:
    """Calculate comprehensive SaaS-specific financial KPIs."""

    def __init__(self, db_session: Optional[Session] = None):
        self.db = db_session
        self.precision = Decimal("0.01")

    def _round_currency(self, value) -> Decimal:
        """Round currency values to 2 decimal places."""
        if isinstance(value, Decimal):
            return value.quantize(self.precision, rounding=ROUND_HALF_UP)
        return Decimal(str(value)).quantize(self.precision, rounding=ROUND_HALF_UP)

    def calculate_comprehensive_kpis(
        self, organization_id: int, period_start: datetime, period_end: datetime
    ) -> Dict[str, Any]:
        """Calculate comprehensive SaaS KPIs."""

        try:
            # Get revenue and customer data
            revenue_data = self._get_revenue_data(organization_id, period_start, period_end)
            customer_data = self._get_customer_data(organization_id, period_start, period_end)
            subscription_data = self._get_subscription_data(organization_id, period_start, period_end)

            # Calculate core SaaS metrics
            kpis = {
                "revenue_metrics": self._calculate_revenue_metrics(revenue_data, subscription_data),
                "customer_metrics": self._calculate_customer_metrics(customer_data, subscription_data),
                "unit_economics": self._calculate_unit_economics(revenue_data, customer_data),
                "growth_metrics": self._calculate_growth_metrics(revenue_data, customer_data),
                "efficiency_metrics": self._calculate_efficiency_metrics(revenue_data, customer_data),
                "cohort_analysis": self._calculate_cohort_metrics(subscription_data),
                "churn_analysis": self._calculate_churn_analysis(subscription_data),
                "expansion_metrics": self._calculate_expansion_metrics(subscription_data),
            }

            # Add benchmarking and scoring
            kpis["benchmarks"] = self._get_industry_benchmarks()
            kpis["health_score"] = self._calculate_saas_health_score(kpis)

            return {
                "organization_id": organization_id,
                "period": {
                    "start": period_start.isoformat(),
                    "end": period_end.isoformat(),
                },
                "kpis": kpis,
                "calculated_at": datetime.now().isoformat(),
                "summary": self._generate_kpi_summary(kpis),
            }

        except Exception as e:
            logger.error(f"Error calculating SaaS KPIs: {e}")
            raise

    def _get_revenue_data(self, organization_id: int, period_start: datetime, period_end: datetime) -> pd.DataFrame:
        """Get revenue data for the specified period."""
        try:
            # This would typically query the database for revenue transactions
            # For now, return mock data structure
            return pd.DataFrame({
                'date': pd.date_range(period_start, period_end, freq='D'),
                'recurring_revenue': [1000 + (i * 10) for i in range((period_end - period_start).days + 1)],
                'one_time_revenue': [100 for _ in range((period_end - period_start).days + 1)],
                'customer_id': [f'cust_{i % 100}' for i in range((period_end - period_start).days + 1)],
            })
        except Exception as e:
            logger.error(f"Error getting revenue data: {e}")
            return pd.DataFrame()

    def _get_customer_data(self, organization_id: int, period_start: datetime, period_end: datetime) -> pd.DataFrame:
        """Get customer data for the specified period."""
        try:
            # Mock customer data
            return pd.DataFrame({
                'customer_id': [f'cust_{i}' for i in range(100)],
                'signup_date': pd.date_range(period_start - timedelta(days=365), period_end, periods=100),
                'churn_date': [None] * 90 + [period_end - timedelta(days=i) for i in range(10)],
                'monthly_value': [50 + (i * 5) for i in range(100)],
                'acquisition_cost': [200 + (i * 10) for i in range(100)],
            })
        except Exception as e:
            logger.error(f"Error getting customer data: {e}")
            return pd.DataFrame()

    def _get_subscription_data(self, organization_id: int, period_start: datetime, period_end: datetime) -> pd.DataFrame:
        """Get subscription data for the specified period."""
        try:
            # Mock subscription data
            return pd.DataFrame({
                'subscription_id': [f'sub_{i}' for i in range(150)],
                'customer_id': [f'cust_{i % 100}' for i in range(150)],
                'start_date': pd.date_range(period_start - timedelta(days=365), period_end, periods=150),
                'end_date': [None] * 135 + [period_end - timedelta(days=i) for i in range(15)],
                'monthly_value': [30 + (i * 3) for i in range(150)],
                'plan_type': ['basic'] * 50 + ['premium'] * 75 + ['enterprise'] * 25,
            })
        except Exception as e:
            logger.error(f"Error getting subscription data: {e}")
            return pd.DataFrame()

    def _calculate_revenue_metrics(self, revenue_data: pd.DataFrame, subscription_data: pd.DataFrame) -> Dict[str, Any]:
        """Calculate comprehensive revenue-specific metrics."""
        try:
            # Monthly Recurring Revenue (MRR)
            mrr = self._calculate_mrr(subscription_data)

            # Annual Recurring Revenue (ARR)
            arr = mrr * 12

            # Revenue growth rates
            revenue_growth = self._calculate_revenue_growth(revenue_data)

            # Revenue churn
            revenue_churn = self._calculate_revenue_churn(subscription_data)

            # Net Revenue Retention (NRR)
            nrr = self._calculate_net_revenue_retention(subscription_data)

            # Gross Revenue Retention (GRR)
            grr = self._calculate_gross_revenue_retention(subscription_data)

            return {
                "monthly_recurring_revenue": float(self._round_currency(mrr)),
                "annual_recurring_revenue": float(self._round_currency(arr)),
                "revenue_growth_rate": revenue_growth,
                "revenue_churn_rate": revenue_churn,
                "net_revenue_retention": nrr,
                "gross_revenue_retention": grr,
                "average_revenue_per_user": float(self._round_currency(self._calculate_arpu(subscription_data))),
                "revenue_per_employee": self._calculate_revenue_per_employee(arr),
            }

        except Exception as e:
            logger.error(f"Error calculating revenue metrics: {e}")
            return {}

    def _calculate_mrr(self, subscription_data: pd.DataFrame) -> Decimal:
        """Calculate Monthly Recurring Revenue."""
        try:
            if subscription_data.empty:
                return Decimal("0")

            # Filter active subscriptions (no end_date or end_date in future)
            current_date = datetime.now()
            active_subs = subscription_data[
                (subscription_data['end_date'].isna()) |
                (pd.to_datetime(subscription_data['end_date']) > current_date)
            ]

            mrr = active_subs['monthly_value'].sum()
            return self._round_currency(mrr)

        except Exception as e:
            logger.error(f"Error calculating MRR: {e}")
            return Decimal("0")

    def _calculate_revenue_growth(self, revenue_data: pd.DataFrame) -> Dict[str, float]:
        """Calculate revenue growth rates."""
        try:
            if revenue_data.empty:
                return {"monthly": 0.0, "quarterly": 0.0, "annual": 0.0}

            # Group by month for monthly growth
            monthly_revenue = revenue_data.groupby(revenue_data['date'].dt.to_period('M'))['recurring_revenue'].sum()

            growth_rates = {}

            # Monthly growth (MoM)
            if len(monthly_revenue) >= 2:
                current_month = monthly_revenue.iloc[-1]
                previous_month = monthly_revenue.iloc[-2]
                growth_rates["monthly"] = ((current_month - previous_month) / previous_month * 100) if previous_month > 0 else 0
            else:
                growth_rates["monthly"] = 0.0

            # Quarterly growth (QoQ)
            quarterly_revenue = revenue_data.groupby(revenue_data['date'].dt.to_period('Q'))['recurring_revenue'].sum()
            if len(quarterly_revenue) >= 2:
                current_quarter = quarterly_revenue.iloc[-1]
                previous_quarter = quarterly_revenue.iloc[-2]
                growth_rates["quarterly"] = ((current_quarter - previous_quarter) / previous_quarter * 100) if previous_quarter > 0 else 0
            else:
                growth_rates["quarterly"] = 0.0

            # Annual growth (YoY)
            if len(monthly_revenue) >= 12:
                current_month = monthly_revenue.iloc[-1]
                year_ago_month = monthly_revenue.iloc[-13]
                growth_rates["annual"] = ((current_month - year_ago_month) / year_ago_month * 100) if year_ago_month > 0 else 0
            else:
                growth_rates["annual"] = 0.0

            return growth_rates

        except Exception as e:
            logger.error(f"Error calculating revenue growth: {e}")
            return {"monthly": 0.0, "quarterly": 0.0, "annual": 0.0}

    def _calculate_revenue_churn(self, subscription_data: pd.DataFrame) -> float:
        """Calculate revenue churn rate."""
        try:
            if subscription_data.empty:
                return 0.0

            current_date = datetime.now()
            start_of_month = current_date.replace(day=1)

            # Revenue at start of month
            start_revenue = subscription_data[
                (pd.to_datetime(subscription_data['start_date']) < start_of_month) &
                ((subscription_data['end_date'].isna()) | (pd.to_datetime(subscription_data['end_date']) >= start_of_month))
            ]['monthly_value'].sum()

            # Revenue lost during month (churned subscriptions)
            churned_revenue = subscription_data[
                (pd.to_datetime(subscription_data['end_date']) >= start_of_month) &
                (pd.to_datetime(subscription_data['end_date']) < current_date)
            ]['monthly_value'].sum()

            churn_rate = (churned_revenue / start_revenue * 100) if start_revenue > 0 else 0
            return churn_rate

        except Exception as e:
            logger.error(f"Error calculating revenue churn: {e}")
            return 0.0

    def _calculate_net_revenue_retention(self, subscription_data: pd.DataFrame) -> float:
        """Calculate Net Revenue Retention (NRR)."""
        try:
            if subscription_data.empty:
                return 0.0

            # This is a simplified calculation
            # In practice, you'd track expansion, contraction, and churn more precisely
            current_date = datetime.now()
            year_ago = current_date - timedelta(days=365)

            # Cohort of customers from a year ago
            cohort_customers = subscription_data[
                pd.to_datetime(subscription_data['start_date']) <= year_ago
            ]['customer_id'].unique()

            # Revenue from this cohort a year ago
            year_ago_revenue = subscription_data[
                (subscription_data['customer_id'].isin(cohort_customers)) &
                (pd.to_datetime(subscription_data['start_date']) <= year_ago)
            ]['monthly_value'].sum()

            # Current revenue from the same cohort (including expansions, minus churn)
            current_cohort_revenue = subscription_data[
                (subscription_data['customer_id'].isin(cohort_customers)) &
                ((subscription_data['end_date'].isna()) | (pd.to_datetime(subscription_data['end_date']) > current_date))
            ]['monthly_value'].sum()

            nrr = (current_cohort_revenue / year_ago_revenue * 100) if year_ago_revenue > 0 else 0
            return nrr

        except Exception as e:
            logger.error(f"Error calculating NRR: {e}")
            return 0.0

    def _calculate_gross_revenue_retention(self, subscription_data: pd.DataFrame) -> float:
        """Calculate Gross Revenue Retention (GRR)."""
        try:
            # GRR excludes expansion revenue, only looks at retention vs churn
            nrr = self._calculate_net_revenue_retention(subscription_data)

            # Simplified: assume some expansion, so GRR is typically lower than NRR
            # In practice, you'd calculate this more precisely
            grr = min(nrr, 95.0)  # Cap at 95% as GRR can't exceed 100%
            return grr

        except Exception as e:
            logger.error(f"Error calculating GRR: {e}")
            return 0.0

    def _calculate_arpu(self, subscription_data: pd.DataFrame) -> Decimal:
        """Calculate Average Revenue Per User (ARPU)."""
        try:
            if subscription_data.empty:
                return Decimal("0")

            current_date = datetime.now()
            active_subs = subscription_data[
                (subscription_data['end_date'].isna()) |
                (pd.to_datetime(subscription_data['end_date']) > current_date)
            ]

            if len(active_subs) == 0:
                return Decimal("0")

            total_revenue = active_subs['monthly_value'].sum()
            unique_customers = active_subs['customer_id'].nunique()

            arpu = total_revenue / unique_customers if unique_customers > 0 else 0
            return self._round_currency(arpu)

        except Exception as e:
            logger.error(f"Error calculating ARPU: {e}")
            return Decimal("0")

    def _calculate_revenue_per_employee(self, arr: Decimal, employee_count: int = 50) -> float:
        """Calculate revenue per employee (using default employee count)."""
        try:
            # In practice, this would come from HR data
            return float(arr / employee_count) if employee_count > 0 else 0
        except Exception as e:
            logger.error(f"Error calculating revenue per employee: {e}")
            return 0.0

    def _calculate_customer_metrics(self, customer_data: pd.DataFrame, subscription_data: pd.DataFrame) -> Dict[str, Any]:
        """Calculate customer-specific metrics."""
        try:
            current_date = datetime.now()

            # Total customers
            total_customers = len(customer_data)

            # Active customers
            active_customers = len(customer_data[customer_data['churn_date'].isna()])

            # New customers this month
            start_of_month = current_date.replace(day=1)
            new_customers = len(customer_data[
                pd.to_datetime(customer_data['signup_date']) >= start_of_month
            ])

            # Churned customers this month
            churned_customers = len(customer_data[
                (pd.to_datetime(customer_data['churn_date']) >= start_of_month) &
                (pd.to_datetime(customer_data['churn_date']) < current_date)
            ])

            # Customer churn rate
            customer_churn_rate = (churned_customers / active_customers * 100) if active_customers > 0 else 0

            # Customer acquisition metrics
            cac = self._calculate_customer_acquisition_cost(customer_data)

            # Customer lifetime value
            ltv = self._calculate_customer_lifetime_value(customer_data, subscription_data)

            # LTV/CAC ratio
            ltv_cac_ratio = float(ltv / cac) if cac > 0 else 0

            return {
                "total_customers": total_customers,
                "active_customers": active_customers,
                "new_customers_this_month": new_customers,
                "churned_customers_this_month": churned_customers,
                "customer_churn_rate": customer_churn_rate,
                "customer_acquisition_cost": float(self._round_currency(cac)),
                "customer_lifetime_value": float(self._round_currency(ltv)),
                "ltv_cac_ratio": ltv_cac_ratio,
                "payback_period_months": self._calculate_payback_period(ltv, cac),
            }

        except Exception as e:
            logger.error(f"Error calculating customer metrics: {e}")
            return {}

    def _calculate_customer_acquisition_cost(self, customer_data: pd.DataFrame) -> Decimal:
        """Calculate Customer Acquisition Cost (CAC)."""
        try:
            if customer_data.empty:
                return Decimal("0")

            # Use the acquisition_cost field if available, otherwise calculate average
            if 'acquisition_cost' in customer_data.columns:
                avg_cac = customer_data['acquisition_cost'].mean()
            else:
                # Default CAC calculation (would typically come from marketing spend data)
                avg_cac = 200  # Default value

            return self._round_currency(avg_cac)

        except Exception as e:
            logger.error(f"Error calculating CAC: {e}")
            return Decimal("200")  # Default fallback

    def _calculate_customer_lifetime_value(self, customer_data: pd.DataFrame, subscription_data: pd.DataFrame) -> Decimal:
        """Calculate Customer Lifetime Value (LTV)."""
        try:
            if customer_data.empty or subscription_data.empty:
                return Decimal("0")

            # Calculate average monthly revenue per customer
            arpu = self._calculate_arpu(subscription_data)

            # Calculate average customer lifespan
            churned_customers = customer_data[customer_data['churn_date'].notna()]

            if len(churned_customers) > 0:
                # Calculate average lifespan for churned customers
                lifespans = []
                for _, customer in churned_customers.iterrows():
                    signup = pd.to_datetime(customer['signup_date'])
                    churn = pd.to_datetime(customer['churn_date'])
                    lifespan_months = (churn - signup).days / 30.44  # Average days per month
                    lifespans.append(lifespan_months)

                avg_lifespan_months = sum(lifespans) / len(lifespans) if lifespans else 12
            else:
                # If no churned customers, use industry average
                avg_lifespan_months = 24  # 2 years default

            # LTV = ARPU * Average Lifespan
            ltv = arpu * Decimal(str(avg_lifespan_months))
            return self._round_currency(ltv)

        except Exception as e:
            logger.error(f"Error calculating LTV: {e}")
            return Decimal("1000")  # Default fallback

    def _calculate_payback_period(self, ltv: Decimal, cac: Decimal) -> float:
        """Calculate payback period in months."""
        try:
            if cac == 0:
                return 0.0

            # Simplified: assume 25% gross margin
            gross_margin = 0.25
            monthly_profit = float(ltv) * gross_margin / 24  # Assume 24 month average lifespan

            payback_months = float(cac) / monthly_profit if monthly_profit > 0 else 0
            return payback_months

        except Exception as e:
            logger.error(f"Error calculating payback period: {e}")
            return 0.0

    def _calculate_unit_economics(self, revenue_data: pd.DataFrame, customer_data: pd.DataFrame) -> Dict[str, Any]:
        """Calculate unit economics metrics."""
        try:
            # Gross margin (simplified calculation)
            total_revenue = revenue_data['recurring_revenue'].sum() if not revenue_data.empty else 0

            # Assume 75% gross margin for SaaS (typical)
            gross_margin_percent = 75.0
            gross_profit = total_revenue * (gross_margin_percent / 100)

            # Unit economics
            cac = self._calculate_customer_acquisition_cost(customer_data)

            # Monthly unit economics
            active_customers = len(customer_data[customer_data['churn_date'].isna()]) if not customer_data.empty else 1
            monthly_gross_profit_per_customer = gross_profit / active_customers if active_customers > 0 else 0

            return {
                "gross_margin_percent": gross_margin_percent,
                "gross_profit": float(self._round_currency(gross_profit)),
                "monthly_gross_profit_per_customer": float(self._round_currency(monthly_gross_profit_per_customer)),
                "customer_acquisition_cost": float(cac),
                "contribution_margin": monthly_gross_profit_per_customer - float(cac),
                "unit_economics_health": "Healthy" if monthly_gross_profit_per_customer > float(cac) else "Needs Improvement",
            }

        except Exception as e:
            logger.error(f"Error calculating unit economics: {e}")
            return {}

    def _calculate_growth_metrics(self, revenue_data: pd.DataFrame, customer_data: pd.DataFrame) -> Dict[str, Any]:
        """Calculate growth-specific metrics."""
        try:
            # Revenue growth (already calculated)
            revenue_growth = self._calculate_revenue_growth(revenue_data)

            # Customer growth
            current_date = datetime.now()

            # Monthly customer growth
            start_of_month = current_date.replace(day=1)
            start_of_prev_month = (start_of_month - timedelta(days=1)).replace(day=1)

            current_month_customers = len(customer_data[
                (pd.to_datetime(customer_data['signup_date']) < current_date) &
                ((customer_data['churn_date'].isna()) | (pd.to_datetime(customer_data['churn_date']) >= current_date))
            ]) if not customer_data.empty else 0

            prev_month_customers = len(customer_data[
                (pd.to_datetime(customer_data['signup_date']) < start_of_month) &
                ((customer_data['churn_date'].isna()) | (pd.to_datetime(customer_data['churn_date']) >= start_of_month))
            ]) if not customer_data.empty else 0

            customer_growth_rate = ((current_month_customers - prev_month_customers) / prev_month_customers * 100) if prev_month_customers > 0 else 0

            # Net new MRR
            new_customers_this_month = len(customer_data[
                pd.to_datetime(customer_data['signup_date']) >= start_of_month
            ]) if not customer_data.empty else 0

            avg_new_customer_value = customer_data['monthly_value'].mean() if not customer_data.empty else 50
            net_new_mrr = new_customers_this_month * avg_new_customer_value

            return {
                "revenue_growth": revenue_growth,
                "customer_growth_rate": customer_growth_rate,
                "net_new_mrr": float(self._round_currency(net_new_mrr)),
                "growth_efficiency": self._calculate_growth_efficiency(revenue_growth, customer_data),
                "viral_coefficient": self._calculate_viral_coefficient(customer_data),
            }

        except Exception as e:
            logger.error(f"Error calculating growth metrics: {e}")
            return {}

    def _calculate_growth_efficiency(self, revenue_growth: Dict[str, float], customer_data: pd.DataFrame) -> float:
        """Calculate growth efficiency (revenue growth per dollar spent on acquisition)."""
        try:
            monthly_growth = revenue_growth.get("monthly", 0)
            avg_cac = float(self._calculate_customer_acquisition_cost(customer_data))

            # Simplified efficiency metric
            efficiency = monthly_growth / avg_cac if avg_cac > 0 else 0
            return efficiency

        except Exception as e:
            logger.error(f"Error calculating growth efficiency: {e}")
            return 0.0

    def _calculate_viral_coefficient(self, customer_data: pd.DataFrame) -> float:
        """Calculate viral coefficient (simplified)."""
        try:
            # This would typically require referral data
            # For now, return a placeholder
            return 0.1  # 10% of customers refer others

        except Exception as e:
            logger.error(f"Error calculating viral coefficient: {e}")
            return 0.0

    def _calculate_efficiency_metrics(self, revenue_data: pd.DataFrame, customer_data: pd.DataFrame) -> Dict[str, Any]:
        """Calculate operational efficiency metrics."""
        try:
            # Sales efficiency
            cac = float(self._calculate_customer_acquisition_cost(customer_data))
            arpu = float(self._calculate_arpu(self._get_subscription_data(1, datetime.now() - timedelta(days=30), datetime.now())))

            # Magic Number (sales efficiency)
            # Simplified: (Net New ARR / Sales & Marketing Spend) * 4
            # Using CAC as proxy for S&M spend per customer
            magic_number = (arpu * 12) / (cac * 4) if cac > 0 else 0

            # Rule of 40 (Growth Rate + Profit Margin should be > 40%)
            revenue_growth = self._calculate_revenue_growth(revenue_data)
            monthly_growth = revenue_growth.get("monthly", 0)
            profit_margin = 25.0  # Assumed profit margin
            rule_of_40 = monthly_growth + profit_margin

            return {
                "magic_number": magic_number,
                "rule_of_40": rule_of_40,
                "sales_efficiency": "Good" if magic_number > 1.0 else "Needs Improvement",
                "cac_payback_period": self._calculate_payback_period(Decimal(str(arpu * 12)), Decimal(str(cac))),
                "revenue_per_employee": self._calculate_revenue_per_employee(Decimal(str(arpu * 12))),
            }

        except Exception as e:
            logger.error(f"Error calculating efficiency metrics: {e}")
            return {}

    def _calculate_cohort_metrics(self, subscription_data: pd.DataFrame) -> Dict[str, Any]:
        """Calculate cohort analysis metrics."""
        try:
            if subscription_data.empty:
                return {}

            # Group customers by signup month
            subscription_data['signup_month'] = pd.to_datetime(subscription_data['start_date']).dt.to_period('M')

            # Calculate retention by cohort
            cohort_data = {}
            current_date = datetime.now()

            for cohort_month in subscription_data['signup_month'].unique():
                cohort_subs = subscription_data[subscription_data['signup_month'] == cohort_month]

                # Calculate retention for different periods
                retention_periods = [1, 3, 6, 12]  # months
                cohort_retention = {}

                for period in retention_periods:
                    period_date = cohort_month.to_timestamp() + timedelta(days=period * 30)

                    if period_date <= current_date:
                        # Count active subscriptions at this period
                        active_at_period = len(cohort_subs[
                            (cohort_subs['end_date'].isna()) |
                            (pd.to_datetime(cohort_subs['end_date']) > period_date)
                        ])

                        retention_rate = (active_at_period / len(cohort_subs) * 100) if len(cohort_subs) > 0 else 0
                        cohort_retention[f"month_{period}"] = retention_rate

                cohort_data[str(cohort_month)] = {
                    "cohort_size": len(cohort_subs),
                    "retention_rates": cohort_retention,
                }

            return {
                "cohort_analysis": cohort_data,
                "average_retention": self._calculate_average_retention(cohort_data),
            }

        except Exception as e:
            logger.error(f"Error calculating cohort metrics: {e}")
            return {}

    def _calculate_average_retention(self, cohort_data: Dict[str, Any]) -> Dict[str, float]:
        """Calculate average retention across cohorts."""
        try:
            retention_periods = ["month_1", "month_3", "month_6", "month_12"]
            avg_retention = {}

            for period in retention_periods:
                period_rates = []
                for cohort_info in cohort_data.values():
                    if period in cohort_info.get("retention_rates", {}):
                        period_rates.append(cohort_info["retention_rates"][period])

                avg_retention[period] = sum(period_rates) / len(period_rates) if period_rates else 0

            return avg_retention

        except Exception as e:
            logger.error(f"Error calculating average retention: {e}")
            return {}

    def _calculate_churn_analysis(self, subscription_data: pd.DataFrame) -> Dict[str, Any]:
        """Calculate detailed churn analysis."""
        try:
            if subscription_data.empty:
                return {}

            current_date = datetime.now()

            # Voluntary vs involuntary churn (simplified)
            churned_subs = subscription_data[subscription_data['end_date'].notna()]

            # Churn by plan type
            churn_by_plan = {}
            for plan_type in subscription_data['plan_type'].unique():
                plan_subs = subscription_data[subscription_data['plan_type'] == plan_type]
                plan_churned = len(plan_subs[plan_subs['end_date'].notna()])
                churn_rate = (plan_churned / len(plan_subs) * 100) if len(plan_subs) > 0 else 0
                churn_by_plan[plan_type] = churn_rate

            # Churn reasons (would typically come from customer feedback)
            churn_reasons = {
                "price": 30,
                "product_fit": 25,
                "competitor": 20,
                "other": 25,
            }

            return {
                "total_churn_rate": self._calculate_revenue_churn(subscription_data),
                "churn_by_plan": churn_by_plan,
                "churn_reasons": churn_reasons,
                "average_time_to_churn": self._calculate_average_time_to_churn(churned_subs),
            }

        except Exception as e:
            logger.error(f"Error calculating churn analysis: {e}")
            return {}

    def _calculate_average_time_to_churn(self, churned_subs: pd.DataFrame) -> float:
        """Calculate average time from signup to churn."""
        try:
            if churned_subs.empty:
                return 0.0

            time_to_churn = []
            for _, sub in churned_subs.iterrows():
                start_date = pd.to_datetime(sub['start_date'])
                end_date = pd.to_datetime(sub['end_date'])
                days_to_churn = (end_date - start_date).days
                time_to_churn.append(days_to_churn)

            avg_days = sum(time_to_churn) / len(time_to_churn) if time_to_churn else 0
            return avg_days / 30.44  # Convert to months

        except Exception as e:
            logger.error(f"Error calculating average time to churn: {e}")
            return 0.0

    def _calculate_expansion_metrics(self, subscription_data: pd.DataFrame) -> Dict[str, Any]:
        """Calculate expansion revenue metrics."""
        try:
            # This would typically require historical subscription value data
            # For now, provide simplified expansion metrics

            return {
                "expansion_mrr": 0.0,  # Would calculate from upgrades
                "contraction_mrr": 0.0,  # Would calculate from downgrades
                "net_expansion_rate": 0.0,  # (Expansion - Contraction) / Starting MRR
                "upsell_rate": 15.0,  # Percentage of customers who upgrade
                "cross_sell_rate": 8.0,  # Percentage who buy additional products
            }

        except Exception as e:
            logger.error(f"Error calculating expansion metrics: {e}")
            return {}

    def _get_industry_benchmarks(self) -> Dict[str, Any]:
        """Get industry benchmark data for SaaS metrics."""
        return {
            "mrr_growth_rate": {"good": 15, "great": 20, "excellent": 25},
            "customer_churn_rate": {"good": 5, "great": 3, "excellent": 2},
            "revenue_churn_rate": {"good": 5, "great": 3, "excellent": 2},
            "ltv_cac_ratio": {"good": 3, "great": 5, "excellent": 7},
            "cac_payback_period": {"good": 12, "great": 6, "excellent": 3},
            "net_revenue_retention": {"good": 100, "great": 110, "excellent": 120},
            "gross_margin": {"good": 70, "great": 80, "excellent": 85},
            "rule_of_40": {"good": 40, "great": 50, "excellent": 60},
        }

    def _calculate_saas_health_score(self, kpis: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate overall SaaS health score based on key metrics."""
        try:
            benchmarks = kpis.get("benchmarks", {})
            scores = {}

            # Revenue metrics scoring (25% weight)
            revenue_metrics = kpis.get("revenue_metrics", {})
            revenue_score = 0

            # MRR growth rate
            mrr_growth = revenue_metrics.get("revenue_growth_rate", {}).get("monthly", 0)
            if mrr_growth >= benchmarks.get("mrr_growth_rate", {}).get("excellent", 25):
                revenue_score += 25
            elif mrr_growth >= benchmarks.get("mrr_growth_rate", {}).get("great", 20):
                revenue_score += 20
            elif mrr_growth >= benchmarks.get("mrr_growth_rate", {}).get("good", 15):
                revenue_score += 15
            else:
                revenue_score += max(0, mrr_growth * 0.6)  # Proportional scoring

            scores["revenue_score"] = min(revenue_score, 25)

            # Customer metrics scoring (25% weight)
            customer_metrics = kpis.get("customer_metrics", {})
            customer_score = 0

            # Customer churn rate (lower is better)
            churn_rate = customer_metrics.get("customer_churn_rate", 10)
            if churn_rate <= benchmarks.get("customer_churn_rate", {}).get("excellent", 2):
                customer_score += 25
            elif churn_rate <= benchmarks.get("customer_churn_rate", {}).get("great", 3):
                customer_score += 20
            elif churn_rate <= benchmarks.get("customer_churn_rate", {}).get("good", 5):
                customer_score += 15
            else:
                customer_score += max(0, 25 - (churn_rate * 2))  # Penalty for high churn

            scores["customer_score"] = min(customer_score, 25)

            # Unit economics scoring (25% weight)
            unit_economics = kpis.get("unit_economics", {})
            unit_score = 0

            # LTV/CAC ratio
            ltv_cac = customer_metrics.get("ltv_cac_ratio", 0)
            if ltv_cac >= benchmarks.get("ltv_cac_ratio", {}).get("excellent", 7):
                unit_score += 25
            elif ltv_cac >= benchmarks.get("ltv_cac_ratio", {}).get("great", 5):
                unit_score += 20
            elif ltv_cac >= benchmarks.get("ltv_cac_ratio", {}).get("good", 3):
                unit_score += 15
            else:
                unit_score += max(0, ltv_cac * 5)  # Proportional scoring

            scores["unit_economics_score"] = min(unit_score, 25)

            # Efficiency metrics scoring (25% weight)
            efficiency_metrics = kpis.get("efficiency_metrics", {})
            efficiency_score = 0

            # Rule of 40
            rule_of_40 = efficiency_metrics.get("rule_of_40", 0)
            if rule_of_40 >= benchmarks.get("rule_of_40", {}).get("excellent", 60):
                efficiency_score += 25
            elif rule_of_40 >= benchmarks.get("rule_of_40", {}).get("great", 50):
                efficiency_score += 20
            elif rule_of_40 >= benchmarks.get("rule_of_40", {}).get("good", 40):
                efficiency_score += 15
            else:
                efficiency_score += max(0, rule_of_40 * 0.4)  # Proportional scoring

            scores["efficiency_score"] = min(efficiency_score, 25)

            # Calculate overall health score
            total_score = sum(scores.values())

            # Determine health grade
            if total_score >= 85:
                health_grade = "A"
                health_status = "Excellent"
            elif total_score >= 70:
                health_grade = "B"
                health_status = "Good"
            elif total_score >= 55:
                health_grade = "C"
                health_status = "Fair"
            elif total_score >= 40:
                health_grade = "D"
                health_status = "Poor"
            else:
                health_grade = "F"
                health_status = "Critical"

            return {
                "overall_score": total_score,
                "health_grade": health_grade,
                "health_status": health_status,
                "component_scores": scores,
                "recommendations": self._generate_health_recommendations(scores, kpis),
            }

        except Exception as e:
            logger.error(f"Error calculating SaaS health score: {e}")
            return {"overall_score": 0, "health_grade": "F", "health_status": "Unknown"}

    def _generate_health_recommendations(self, scores: Dict[str, float], kpis: Dict[str, Any]) -> List[str]:
        """Generate recommendations based on health score components."""
        recommendations = []

        # Revenue recommendations
        if scores.get("revenue_score", 0) < 15:
            recommendations.append("Focus on accelerating revenue growth through improved sales and marketing")

        # Customer recommendations
        if scores.get("customer_score", 0) < 15:
            recommendations.append("Reduce customer churn through improved onboarding and customer success")

        # Unit economics recommendations
        if scores.get("unit_economics_score", 0) < 15:
            recommendations.append("Improve unit economics by optimizing CAC and increasing customer lifetime value")

        # Efficiency recommendations
        if scores.get("efficiency_score", 0) < 15:
            recommendations.append("Enhance operational efficiency to improve the Rule of 40 score")

        # General recommendations
        customer_metrics = kpis.get("customer_metrics", {})
        ltv_cac = customer_metrics.get("ltv_cac_ratio", 0)

        if ltv_cac < 3:
            recommendations.append("Critical: LTV/CAC ratio is below healthy threshold - review pricing and acquisition strategy")

        return recommendations

    def _generate_kpi_summary(self, kpis: Dict[str, Any]) -> Dict[str, Any]:
        """Generate executive summary of key SaaS KPIs."""
        try:
            revenue_metrics = kpis.get("revenue_metrics", {})
            customer_metrics = kpis.get("customer_metrics", {})
            unit_economics = kpis.get("unit_economics", {})
            health_score = kpis.get("health_score", {})

            # Key highlights
            highlights = []

            mrr = revenue_metrics.get("monthly_recurring_revenue", 0)
            if mrr > 0:
                highlights.append(f"Monthly Recurring Revenue: ${mrr:,.2f}")

            arr = revenue_metrics.get("annual_recurring_revenue", 0)
            if arr > 0:
                highlights.append(f"Annual Recurring Revenue: ${arr:,.2f}")

            growth_rate = revenue_metrics.get("revenue_growth_rate", {}).get("monthly", 0)
            if growth_rate > 0:
                highlights.append(f"Monthly Growth Rate: {growth_rate:.1f}%")

            ltv_cac = customer_metrics.get("ltv_cac_ratio", 0)
            if ltv_cac > 0:
                highlights.append(f"LTV/CAC Ratio: {ltv_cac:.1f}x")

            # Key concerns
            concerns = []

            churn_rate = customer_metrics.get("customer_churn_rate", 0)
            if churn_rate > 5:
                concerns.append(f"High customer churn rate: {churn_rate:.1f}%")

            if ltv_cac < 3:
                concerns.append("LTV/CAC ratio below healthy threshold")

            payback_period = customer_metrics.get("payback_period_months", 0)
            if payback_period > 12:
                concerns.append(f"Long CAC payback period: {payback_period:.1f} months")

            return {
                "health_status": health_score.get("health_status", "Unknown"),
                "overall_score": health_score.get("overall_score", 0),
                "key_highlights": highlights,
                "key_concerns": concerns if concerns else ["No major concerns identified"],
                "top_metrics": {
                    "mrr": mrr,
                    "arr": arr,
                    "growth_rate": growth_rate,
                    "churn_rate": churn_rate,
                    "ltv_cac_ratio": ltv_cac,
                },
                "next_actions": self._generate_next_actions(kpis),
            }

        except Exception as e:
            logger.error(f"Error generating KPI summary: {e}")
            return {"error": str(e)}

    def _generate_next_actions(self, kpis: Dict[str, Any]) -> List[str]:
        """Generate recommended next actions based on KPI analysis."""
        actions = []

        customer_metrics = kpis.get("customer_metrics", {})
        revenue_metrics = kpis.get("revenue_metrics", {})

        # Growth actions
        growth_rate = revenue_metrics.get("revenue_growth_rate", {}).get("monthly", 0)
        if growth_rate < 10:
            actions.append("Develop growth acceleration plan focusing on customer acquisition")

        # Retention actions
        churn_rate = customer_metrics.get("customer_churn_rate", 0)
        if churn_rate > 5:
            actions.append("Implement customer success program to reduce churn")

        # Unit economics actions
        ltv_cac = customer_metrics.get("ltv_cac_ratio", 0)
        if ltv_cac < 3:
            actions.append("Optimize pricing strategy and reduce customer acquisition costs")

        # Default actions if metrics are healthy
        if not actions:
            actions.extend([
                "Continue monitoring key metrics and maintain current performance",
                "Explore expansion opportunities with existing customers",
                "Invest in product development to maintain competitive advantage",
            ])

        return actions
