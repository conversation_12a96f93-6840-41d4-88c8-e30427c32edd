"""
Data enrichment module with industry classifications and metadata enhancement.
Provides intelligent data enrichment for financial records with business intelligence.
"""

from typing import Dict, Optional, Any, List, Set, Tuple
from decimal import Decimal
import logging
from enum import Enum
from dataclasses import dataclass
import re
from functools import lru_cache
from datetime import datetime

logger = logging.getLogger(__name__)


class IndustryClassification(Enum):
    """Industry classification codes (NAICS-based)."""
    # Technology
    SOFTWARE_PUBLISHERS = "5112"
    COMPUTER_SYSTEMS_DESIGN = "5415"
    DATA_PROCESSING_HOSTING = "5182"
    TELECOMMUNICATIONS = "5171"
    
    # Manufacturing
    FOOD_MANUFACTURING = "311"
    TEXTILE_MANUFACTURING = "313"
    CHEMICAL_MANUFACTURING = "325"
    MACHINERY_MANUFACTURING = "333"
    
    # Retail
    MOTOR_VEHICLE_DEALERS = "4411"
    FURNITURE_STORES = "4421"
    ELECTRONICS_STORES = "4431"
    CLOTHING_STORES = "4481"
    
    # Healthcare
    AMBULATORY_HEALTHCARE = "621"
    HOSPITALS = "622"
    NURSING_FACILITIES = "623"
    
    # Financial Services
    COMMERCIAL_BANKING = "5221"
    INSURANCE_CARRIERS = "5241"
    INVESTMENT_BANKING = "5231"
    
    # Professional Services
    LEGAL_SERVICES = "5411"
    ACCOUNTING_SERVICES = "5412"
    CONSULTING_SERVICES = "5416"
    
    # Real Estate
    REAL_ESTATE_LEASING = "5311"
    REAL_ESTATE_SERVICES = "5312"
    
    # Construction
    RESIDENTIAL_CONSTRUCTION = "2361"
    COMMERCIAL_CONSTRUCTION = "2362"
    
    # Other
    AGRICULTURE = "11"
    MINING = "21"
    UTILITIES = "22"
    TRANSPORTATION = "48"
    EDUCATION = "61"
    ARTS_ENTERTAINMENT = "71"
    ACCOMMODATION_FOOD = "72"
    OTHER_SERVICES = "81"
    PUBLIC_ADMINISTRATION = "92"
    UNKNOWN = "0000"


class BusinessSize(Enum):
    """Business size classifications."""
    MICRO = "micro"          # < 10 employees, < $1M revenue
    SMALL = "small"          # 10-49 employees, $1M-$10M revenue
    MEDIUM = "medium"        # 50-249 employees, $10M-$50M revenue
    LARGE = "large"          # 250+ employees, $50M+ revenue
    ENTERPRISE = "enterprise" # 1000+ employees, $250M+ revenue
    UNKNOWN = "unknown"


class GeographicRegion(Enum):
    """Geographic region classifications."""
    NORTH_AMERICA = "north_america"
    EUROPE = "europe"
    ASIA_PACIFIC = "asia_pacific"
    LATIN_AMERICA = "latin_america"
    MIDDLE_EAST_AFRICA = "middle_east_africa"
    UNKNOWN = "unknown"


@dataclass
class EnrichmentMetadata:
    """Metadata for data enrichment."""
    enrichment_date: datetime
    data_sources: List[str]
    confidence_score: float
    enrichment_version: str = "1.0"
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization."""
        return {
            "enrichment_date": self.enrichment_date.isoformat(),
            "data_sources": self.data_sources,
            "confidence_score": self.confidence_score,
            "enrichment_version": self.enrichment_version
        }


@dataclass
class BusinessProfile:
    """Enhanced business profile with classifications."""
    industry_classification: IndustryClassification
    business_size: BusinessSize
    geographic_region: GeographicRegion
    annual_revenue_estimate: Optional[Decimal] = None
    employee_count_estimate: Optional[int] = None
    business_age_years: Optional[int] = None
    credit_rating: Optional[str] = None
    risk_score: Optional[float] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization."""
        return {
            "industry_classification": self.industry_classification.value,
            "business_size": self.business_size.value,
            "geographic_region": self.geographic_region.value,
            "annual_revenue_estimate": float(self.annual_revenue_estimate) if self.annual_revenue_estimate else None,
            "employee_count_estimate": self.employee_count_estimate,
            "business_age_years": self.business_age_years,
            "credit_rating": self.credit_rating,
            "risk_score": self.risk_score
        }


class DataEnrichmentEngine:
    """Main data enrichment engine with industry intelligence."""
    
    def __init__(self):
        self.industry_patterns = self._load_industry_patterns()
        self.business_size_rules = self._load_business_size_rules()
        self.geographic_patterns = self._load_geographic_patterns()
        self._enrichment_cache: Dict[str, Dict[str, Any]] = {}
    
    def _load_industry_patterns(self) -> Dict[IndustryClassification, List[str]]:
        """Load industry classification patterns."""
        return {
            IndustryClassification.SOFTWARE_PUBLISHERS: [
                r"software|saas|app|platform|tech|digital",
                r"microsoft|google|adobe|salesforce|oracle"
            ],
            IndustryClassification.COMPUTER_SYSTEMS_DESIGN: [
                r"systems|consulting|it services|development",
                r"accenture|ibm|cognizant|infosys"
            ],
            IndustryClassification.TELECOMMUNICATIONS: [
                r"telecom|wireless|internet|broadband|phone",
                r"verizon|att|comcast|sprint"
            ],
            IndustryClassification.FOOD_MANUFACTURING: [
                r"food|beverage|restaurant|catering|dining",
                r"coca.cola|pepsi|nestle|unilever"
            ],
            IndustryClassification.COMMERCIAL_BANKING: [
                r"bank|banking|financial|credit|loan",
                r"chase|wells.fargo|bank.of.america|citibank"
            ],
            IndustryClassification.LEGAL_SERVICES: [
                r"law|legal|attorney|lawyer|counsel",
                r"llp|associates|partners"
            ],
            IndustryClassification.ACCOUNTING_SERVICES: [
                r"accounting|cpa|audit|tax|bookkeeping",
                r"deloitte|pwc|ernst.young|kpmg"
            ],
            IndustryClassification.CONSULTING_SERVICES: [
                r"consulting|advisory|strategy|management",
                r"mckinsey|bain|bcg|deloitte"
            ],
            IndustryClassification.REAL_ESTATE_LEASING: [
                r"real.estate|property|leasing|rental",
                r"realty|properties|estates"
            ],
            IndustryClassification.RESIDENTIAL_CONSTRUCTION: [
                r"construction|building|contractor|home",
                r"residential|housing|development"
            ],
            IndustryClassification.AMBULATORY_HEALTHCARE: [
                r"health|medical|clinic|doctor|ambulatory",
                r"healthcare|outpatient|medical services"
            ],
            IndustryClassification.HOSPITALS: [
                r"hospital|medical center|health system",
                r"inpatient|emergency|surgical"
            ],
            IndustryClassification.NURSING_FACILITIES: [
                r"nursing|care facility|assisted living",
                r"elderly care|rehabilitation|long-term care"
            ],
            IndustryClassification.MOTOR_VEHICLE_DEALERS: [
                r"auto|car|vehicle|dealer|automotive",
                r"ford|toyota|honda|chevrolet"
            ],
            IndustryClassification.FURNITURE_STORES: [
                r"furniture|home|decor|furnishing",
                r"ikea|ashley|wayfair"
            ],
            IndustryClassification.ELECTRONICS_STORES: [
                r"electronics|computer|tech|gadget",
                r"best.buy|apple.store|microsoft"
            ],
            IndustryClassification.CLOTHING_STORES: [
                r"clothing|apparel|fashion|retail",
                r"walmart|target|amazon|costco|macy"
            ],
            IndustryClassification.FOOD_MANUFACTURING: [
                r"food|beverage|manufacturing|processing",
                r"coca.cola|pepsi|nestle|kraft"
            ],
            IndustryClassification.CHEMICAL_MANUFACTURING: [
                r"chemical|pharmaceutical|manufacturing|industrial",
                r"dupont|dow|basf|3m"
            ],
            IndustryClassification.MACHINERY_MANUFACTURING: [
                r"machinery|equipment|manufacturing|industrial",
                r"general.electric|boeing|caterpillar"
            ]
        }
    
    def _load_business_size_rules(self) -> List[Dict[str, Any]]:
        """Load business size classification rules."""
        return [
            {
                "size": BusinessSize.MICRO,
                "revenue_max": Decimal("1000000"),
                "employee_max": 10,
                "indicators": ["llc", "sole proprietor", "freelance", "consultant"]
            },
            {
                "size": BusinessSize.SMALL,
                "revenue_min": Decimal("1000000"),
                "revenue_max": Decimal("10000000"),
                "employee_min": 10,
                "employee_max": 49,
                "indicators": ["inc", "corp", "small business"]
            },
            {
                "size": BusinessSize.MEDIUM,
                "revenue_min": Decimal("10000000"),
                "revenue_max": Decimal("50000000"),
                "employee_min": 50,
                "employee_max": 249,
                "indicators": ["corporation", "company"]
            },
            {
                "size": BusinessSize.LARGE,
                "revenue_min": Decimal("50000000"),
                "revenue_max": Decimal("*********"),
                "employee_min": 250,
                "employee_max": 999,
                "indicators": ["corporation", "enterprises"]
            },
            {
                "size": BusinessSize.ENTERPRISE,
                "revenue_min": Decimal("*********"),
                "employee_min": 1000,
                "indicators": ["corporation", "international", "global", "worldwide"]
            }
        ]
    
    def _load_geographic_patterns(self) -> Dict[GeographicRegion, List[str]]:
        """Load geographic region patterns."""
        return {
            GeographicRegion.NORTH_AMERICA: [
                r"usa|united.states|canada|mexico|us|ca",
                r"new.york|california|texas|florida|ontario"
            ],
            GeographicRegion.EUROPE: [
                r"uk|united.kingdom|germany|france|italy|spain",
                r"london|berlin|paris|madrid|amsterdam"
            ],
            GeographicRegion.ASIA_PACIFIC: [
                r"china|japan|india|australia|singapore|korea",
                r"beijing|tokyo|mumbai|sydney|seoul"
            ],
            GeographicRegion.LATIN_AMERICA: [
                r"brazil|argentina|chile|colombia|peru",
                r"sao.paulo|buenos.aires|santiago|bogota"
            ],
            GeographicRegion.MIDDLE_EAST_AFRICA: [
                r"uae|saudi|israel|south.africa|egypt",
                r"dubai|riyadh|tel.aviv|johannesburg|cairo"
            ]
        }
    
    @lru_cache(maxsize=1000)
    def classify_industry(self, business_name: str, description: str = "") -> IndustryClassification:
        """Classify industry based on business name and description."""
        try:
            combined_text = f"{business_name} {description}".lower()
            
            best_match = IndustryClassification.UNKNOWN
            best_score = 0
            
            for industry, patterns in self.industry_patterns.items():
                score = 0
                for pattern in patterns:
                    if re.search(pattern, combined_text, re.IGNORECASE):
                        score += 1
                
                if score > best_score:
                    best_match = industry
                    best_score = score
            
            return best_match
            
        except Exception as e:
            logger.error(f"Error classifying industry: {e}")
            return IndustryClassification.UNKNOWN
    
    def classify_business_size(
        self,
        annual_revenue: Optional[Decimal] = None,
        employee_count: Optional[int] = None,
        business_name: str = ""
    ) -> BusinessSize:
        """Classify business size based on revenue, employees, and indicators."""
        try:
            business_name_lower = business_name.lower()
            
            for rule in self.business_size_rules:
                size = rule["size"]
                matches = 0
                total_criteria = 0
                
                # Check revenue criteria
                if annual_revenue and "revenue_min" in rule:
                    total_criteria += 1
                    if annual_revenue >= rule.get("revenue_min", 0):
                        matches += 1
                
                if annual_revenue and "revenue_max" in rule:
                    total_criteria += 1
                    if annual_revenue <= rule.get("revenue_max", float("inf")):
                        matches += 1
                
                # Check employee criteria
                if employee_count and "employee_min" in rule:
                    total_criteria += 1
                    if employee_count >= rule.get("employee_min", 0):
                        matches += 1
                
                if employee_count and "employee_max" in rule:
                    total_criteria += 1
                    if employee_count <= rule.get("employee_max", float("inf")):
                        matches += 1
                
                # Check name indicators
                if business_name:
                    total_criteria += 1
                    if any(indicator in business_name_lower for indicator in rule.get("indicators", [])):
                        matches += 1
                
                # If we have a good match (>= 50% criteria met)
                if total_criteria > 0 and matches / total_criteria >= 0.5:
                    return size
            
            return BusinessSize.UNKNOWN
            
        except Exception as e:
            logger.error(f"Error classifying business size: {e}")
            return BusinessSize.UNKNOWN
    
    def classify_geographic_region(self, address: str = "", business_name: str = "") -> GeographicRegion:
        """Classify geographic region based on address and business name."""
        try:
            combined_text = f"{address} {business_name}".lower()
            
            for region, patterns in self.geographic_patterns.items():
                for pattern in patterns:
                    if re.search(pattern, combined_text, re.IGNORECASE):
                        return region
            
            return GeographicRegion.UNKNOWN
            
        except Exception as e:
            logger.error(f"Error classifying geographic region: {e}")
            return GeographicRegion.UNKNOWN

    def enrich_contact_data(self, contact: Dict[str, Any]) -> Dict[str, Any]:
        """Enrich contact data with business intelligence."""
        try:
            contact_name = contact.get("name", "")
            address = contact.get("address", "")

            # Create business profile
            industry = self.classify_industry(contact_name)
            business_size = self.classify_business_size(business_name=contact_name)
            geographic_region = self.classify_geographic_region(address, contact_name)

            # Estimate annual revenue based on industry and size
            revenue_estimate = self._estimate_annual_revenue(industry, business_size)

            # Estimate employee count
            employee_estimate = self._estimate_employee_count(business_size)

            # Calculate risk score
            risk_score = self._calculate_risk_score(industry, business_size, geographic_region)

            business_profile = BusinessProfile(
                industry_classification=industry,
                business_size=business_size,
                geographic_region=geographic_region,
                annual_revenue_estimate=revenue_estimate,
                employee_count_estimate=employee_estimate,
                risk_score=risk_score
            )

            # Create enriched contact
            enriched_contact = contact.copy()
            enriched_contact["business_profile"] = business_profile.to_dict()
            enriched_contact["enrichment_metadata"] = EnrichmentMetadata(
                enrichment_date=datetime.utcnow(),
                data_sources=["industry_patterns", "business_size_rules", "geographic_patterns"],
                confidence_score=self._calculate_enrichment_confidence(business_profile)
            ).to_dict()

            return enriched_contact

        except Exception as e:
            logger.error(f"Error enriching contact data: {e}")
            return contact

    def _estimate_annual_revenue(self, industry: IndustryClassification, business_size: BusinessSize) -> Optional[Decimal]:
        """Estimate annual revenue based on industry and business size."""
        try:
            # Base estimates by business size
            size_estimates = {
                BusinessSize.MICRO: Decimal("500000"),
                BusinessSize.SMALL: Decimal("5000000"),
                BusinessSize.MEDIUM: Decimal("********"),
                BusinessSize.LARGE: Decimal("*********"),
                BusinessSize.ENTERPRISE: Decimal("*********")
            }

            base_estimate = size_estimates.get(business_size)
            if not base_estimate:
                return None

            # Industry multipliers
            industry_multipliers = {
                IndustryClassification.SOFTWARE_PUBLISHERS: Decimal("1.5"),
                IndustryClassification.COMMERCIAL_BANKING: Decimal("2.0"),
                IndustryClassification.TELECOMMUNICATIONS: Decimal("1.3"),
                IndustryClassification.AMBULATORY_HEALTHCARE: Decimal("1.2"),
                IndustryClassification.MACHINERY_MANUFACTURING: Decimal("1.1"),
                IndustryClassification.ELECTRONICS_STORES: Decimal("0.9"),
                IndustryClassification.FOOD_MANUFACTURING: Decimal("0.8")
            }

            multiplier = industry_multipliers.get(industry, Decimal("1.0"))
            return base_estimate * multiplier

        except Exception as e:
            logger.error(f"Error estimating annual revenue: {e}")
            return None

    def _estimate_employee_count(self, business_size: BusinessSize) -> Optional[int]:
        """Estimate employee count based on business size."""
        try:
            size_estimates = {
                BusinessSize.MICRO: 5,
                BusinessSize.SMALL: 25,
                BusinessSize.MEDIUM: 150,
                BusinessSize.LARGE: 500,
                BusinessSize.ENTERPRISE: 2500
            }

            return size_estimates.get(business_size)

        except Exception as e:
            logger.error(f"Error estimating employee count: {e}")
            return None

    def _calculate_risk_score(
        self,
        industry: IndustryClassification,
        business_size: BusinessSize,
        geographic_region: GeographicRegion
    ) -> float:
        """Calculate risk score based on various factors."""
        try:
            base_score = 0.5  # Neutral risk

            # Industry risk adjustments
            industry_risk = {
                IndustryClassification.SOFTWARE_PUBLISHERS: -0.1,  # Lower risk
                IndustryClassification.COMMERCIAL_BANKING: -0.2,   # Lower risk (regulated)
                IndustryClassification.RESIDENTIAL_CONSTRUCTION: 0.2,  # Higher risk
                IndustryClassification.ELECTRONICS_STORES: 0.1,       # Slightly higher risk
                IndustryClassification.AMBULATORY_HEALTHCARE: -0.1    # Lower risk
            }

            # Business size risk adjustments
            size_risk = {
                BusinessSize.ENTERPRISE: -0.2,  # Lower risk (more stable)
                BusinessSize.LARGE: -0.1,
                BusinessSize.MEDIUM: 0.0,
                BusinessSize.SMALL: 0.1,
                BusinessSize.MICRO: 0.2         # Higher risk
            }

            # Geographic risk adjustments
            region_risk = {
                GeographicRegion.NORTH_AMERICA: -0.1,
                GeographicRegion.EUROPE: -0.05,
                GeographicRegion.ASIA_PACIFIC: 0.0,
                GeographicRegion.LATIN_AMERICA: 0.1,
                GeographicRegion.MIDDLE_EAST_AFRICA: 0.15
            }

            # Calculate final score
            final_score = (
                base_score +
                industry_risk.get(industry, 0.0) +
                size_risk.get(business_size, 0.0) +
                region_risk.get(geographic_region, 0.0)
            )

            # Ensure score is between 0 and 1
            return max(0.0, min(1.0, final_score))

        except Exception as e:
            logger.error(f"Error calculating risk score: {e}")
            return 0.5

    def _calculate_enrichment_confidence(self, business_profile: BusinessProfile) -> float:
        """Calculate confidence score for enrichment."""
        try:
            confidence = 0.0

            # Industry classification confidence
            if business_profile.industry_classification != IndustryClassification.UNKNOWN:
                confidence += 0.3

            # Business size confidence
            if business_profile.business_size != BusinessSize.UNKNOWN:
                confidence += 0.3

            # Geographic region confidence
            if business_profile.geographic_region != GeographicRegion.UNKNOWN:
                confidence += 0.2

            # Revenue estimate confidence
            if business_profile.annual_revenue_estimate:
                confidence += 0.1

            # Employee estimate confidence
            if business_profile.employee_count_estimate:
                confidence += 0.1

            return confidence

        except Exception as e:
            logger.error(f"Error calculating enrichment confidence: {e}")
            return 0.0
