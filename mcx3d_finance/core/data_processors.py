"""
Enhanced data processing module with GAAP compliance and NASDAQ reporting standards.
"""

from typing import Dict, Optional, Any, List
from decimal import Decimal, ROUND_HALF_UP
import logging
from enum import Enum
from datetime import datetime
import re
import time

from mcx3d_finance.core.config import get_xero_config
from mcx3d_finance.core.currency_converter import CurrencyConverter
from mcx3d_finance.core.account_mapper import AdvancedAccountMapper, IndustryType
from mcx3d_finance.core.transaction_classifier import TransactionClassifier
from mcx3d_finance.core.data_enrichment import DataEnrichmentEngine
from mcx3d_finance.core.duplicate_detector import DuplicateDetector, MergeEngine, ConfidenceLevel
from mcx3d_finance.core.transformation_engine import (
    BatchTransformationEngine, TransformationRule, TransformationRuleType,
    DataQualityScorer, DataQualityLevel
)
from mcx3d_finance.core.validation_integration import (
    IntegratedValidationEngine, ValidationContext, ValidationTrigger,
    ValidationAction, DataRoute
)
from mcx3d_finance.core.account_classifications import GAAPAccountClassification
from mcx3d_finance.core.data_validation import DataValidationEngine

logger = logging.getLogger(__name__)



class XeroDataProcessor:
    """Enhanced Xero data processor with GAAP compliance and NASDAQ standards."""

    def __init__(self, base_currency: str = "USD", industry: IndustryType = IndustryType.GENERAL):
        self.xero_config = get_xero_config()
        self.gaap_mappings = self._load_gaap_account_mappings()
        self.precision = Decimal("0.01")
        self.base_currency = base_currency
        self.industry = industry
        self.currency_converter = CurrencyConverter(base_currency=base_currency)
        self.account_mapper = AdvancedAccountMapper(industry=industry)
        self.transaction_classifier = TransactionClassifier()
        self.data_enrichment_engine = DataEnrichmentEngine()
        self.duplicate_detector = DuplicateDetector()
        self.merge_engine = MergeEngine()
        self.transformation_engine = BatchTransformationEngine()
        self.quality_scorer = DataQualityScorer()
        # Initialize validation engine first
        self.validation_engine = DataValidationEngine()
        self.integrated_validation = IntegratedValidationEngine(self.validation_engine)

        # Initialize default transformation rules
        self._setup_default_transformation_rules()

    def _load_gaap_account_mappings(self) -> Dict[str, GAAPAccountClassification]:
        """Load GAAP-compliant account mappings for NASDAQ reporting."""
        return {
            # Cash and Cash Equivalents
            "1010": GAAPAccountClassification.CASH_AND_EQUIVALENTS,
            "1020": GAAPAccountClassification.CASH_AND_EQUIVALENTS,
            "1030": GAAPAccountClassification.CASH_AND_EQUIVALENTS,
            # Accounts Receivable
            "1200": GAAPAccountClassification.ACCOUNTS_RECEIVABLE,
            "1210": GAAPAccountClassification.ACCOUNTS_RECEIVABLE,
            # Inventory
            "1300": GAAPAccountClassification.INVENTORY,
            "1310": GAAPAccountClassification.INVENTORY,
            # Prepaid Expenses
            "1400": GAAPAccountClassification.PREPAID_EXPENSES,
            "1410": GAAPAccountClassification.PREPAID_EXPENSES,
            # Property, Plant & Equipment
            "1600": GAAPAccountClassification.PROPERTY_PLANT_EQUIPMENT,
            "1610": GAAPAccountClassification.PROPERTY_PLANT_EQUIPMENT,
            "1620": GAAPAccountClassification.PROPERTY_PLANT_EQUIPMENT,
            # Intangible Assets
            "1700": GAAPAccountClassification.INTANGIBLE_ASSETS,
            "1710": GAAPAccountClassification.GOODWILL,
            # Accounts Payable
            "2000": GAAPAccountClassification.ACCOUNTS_PAYABLE,
            "2010": GAAPAccountClassification.ACCOUNTS_PAYABLE,
            # Accrued Liabilities
            "2100": GAAPAccountClassification.ACCRUED_LIABILITIES,
            "2110": GAAPAccountClassification.ACCRUED_LIABILITIES,
            # Short-term Debt
            "2200": GAAPAccountClassification.SHORT_TERM_DEBT,
            "2210": GAAPAccountClassification.CURRENT_PORTION_LONG_TERM_DEBT,
            # Long-term Debt
            "2400": GAAPAccountClassification.LONG_TERM_DEBT,
            "2410": GAAPAccountClassification.LONG_TERM_DEBT,
            # Equity
            "3000": GAAPAccountClassification.COMMON_STOCK,
            "3100": GAAPAccountClassification.RETAINED_EARNINGS,
            "3200": GAAPAccountClassification.ADDITIONAL_PAID_IN_CAPITAL,
            # Revenue
            "4000": GAAPAccountClassification.PRODUCT_REVENUE,
            "4100": GAAPAccountClassification.SERVICE_REVENUE,
            "4200": GAAPAccountClassification.SUBSCRIPTION_REVENUE,
            "4900": GAAPAccountClassification.OTHER_REVENUE,
            # Cost of Revenue
            "5000": GAAPAccountClassification.COST_OF_GOODS_SOLD,
            "5100": GAAPAccountClassification.COST_OF_SERVICES,
            # Operating Expenses
            "6000": GAAPAccountClassification.SALES_AND_MARKETING,
            "6100": GAAPAccountClassification.RESEARCH_AND_DEVELOPMENT,
            "6200": GAAPAccountClassification.GENERAL_AND_ADMINISTRATIVE,
            "6300": GAAPAccountClassification.DEPRECIATION_AND_AMORTIZATION,
            # Non-operating
            "7000": GAAPAccountClassification.INTEREST_EXPENSE,
            "7900": GAAPAccountClassification.OTHER_EXPENSE,
        }

    def _round_currency(self, value: float) -> Decimal:
        """Round currency values to 2 decimal places for GAAP compliance."""
        return Decimal(str(value)).quantize(self.precision, rounding=ROUND_HALF_UP)

    def process_trial_balance_for_gaap(
        self, trial_balance_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Process trial balance data into GAAP-compliant format for NASDAQ reporting."""
        try:
            logger.info("Processing trial balance for GAAP compliance")

            gaap_balances = {
                "report_date": trial_balance_data["report_date"],
                "assets": {
                    "current_assets": {},
                    "non_current_assets": {},
                    "total_assets": Decimal("0"),
                },
                "liabilities": {
                    "current_liabilities": {},
                    "non_current_liabilities": {},
                    "total_liabilities": Decimal("0"),
                },
                "equity": {"stockholders_equity": {}, "total_equity": Decimal("0")},
            }

            for account in trial_balance_data.get("accounts", []):
                account_code = account.get("account_code", "")
                account_name = account.get("account_name", "")
                debit = self._round_currency(account.get("debit", 0))
                credit = self._round_currency(account.get("credit", 0))
                net_balance = debit - credit

                # Skip zero balances
                if net_balance == 0:
                    continue

                # Classify account using GAAP mapping
                gaap_classification = self._classify_account_gaap(account_code)

                if gaap_classification:
                    self._add_to_gaap_balance(
                        gaap_balances, gaap_classification, account_name, net_balance
                    )

            # Calculate totals
            self._calculate_gaap_totals(gaap_balances)

            # Validate balance sheet equation (Assets = Liabilities + Equity)
            self._validate_balance_sheet_equation(gaap_balances)

            return gaap_balances

        except Exception as e:
            logger.error(f"Error processing trial balance for GAAP: {e}")
            raise

    def process_profit_loss_for_gaap(self, pl_data: Dict[str, Any]) -> Dict[str, Any]:
        """Process P&L data into GAAP-compliant income statement format."""
        try:
            logger.info("Processing P&L for GAAP compliance")

            gaap_income_statement = {
                "period_start": pl_data["from_date"],
                "period_end": pl_data["to_date"],
                "revenue": {"total_revenue": Decimal("0"), "breakdown": {}},
                "cost_of_revenue": {
                    "total_cost_of_revenue": Decimal("0"),
                    "breakdown": {},
                },
                "gross_profit": Decimal("0"),
                "operating_expenses": {
                    "total_operating_expenses": Decimal("0"),
                    "breakdown": {},
                },
                "operating_income": Decimal("0"),
                "non_operating_income_expense": {
                    "total_non_operating": Decimal("0"),
                    "breakdown": {},
                },
                "income_before_taxes": Decimal("0"),
                "income_tax_expense": Decimal("0"),
                "net_income": Decimal("0"),
            }

            # Process each section from Xero P&L
            for section in pl_data.get("sections", []):
                section_name = section.get("section_name", "").lower()

                for account in section.get("accounts", []):
                    account_name = account.get("account_name", "")
                    amount = self._round_currency(account.get("amount", 0))

                    # Classify and add to appropriate GAAP section
                    if "revenue" in section_name or "income" in section_name:
                        gaap_income_statement["revenue"]["breakdown"][account_name] = (
                            float(amount)
                        )
                        gaap_income_statement["revenue"]["total_revenue"] += amount
                    elif (
                        "cost of sales" in section_name
                        or "cost of goods" in section_name
                    ):
                        gaap_income_statement["cost_of_revenue"]["breakdown"][
                            account_name
                        ] = float(amount)
                        gaap_income_statement["cost_of_revenue"][
                            "total_cost_of_revenue"
                        ] += amount
                    elif "expense" in section_name:
                        gaap_income_statement["operating_expenses"]["breakdown"][
                            account_name
                        ] = float(amount)
                        gaap_income_statement["operating_expenses"][
                            "total_operating_expenses"
                        ] += amount

            # Calculate derived amounts
            gaap_income_statement["gross_profit"] = (
                gaap_income_statement["revenue"]["total_revenue"]
                - gaap_income_statement["cost_of_revenue"]["total_cost_of_revenue"]
            )

            gaap_income_statement["operating_income"] = (
                gaap_income_statement["gross_profit"]
                - gaap_income_statement["operating_expenses"][
                    "total_operating_expenses"
                ]
            )

            gaap_income_statement["income_before_taxes"] = (
                gaap_income_statement["operating_income"]
                + gaap_income_statement["non_operating_income_expense"][
                    "total_non_operating"
                ]
            )

            gaap_income_statement["net_income"] = (
                gaap_income_statement["income_before_taxes"]
                - gaap_income_statement["income_tax_expense"]
            )

            # Convert Decimal to float for JSON serialization
            self._convert_decimals_to_float(gaap_income_statement)

            return gaap_income_statement

        except Exception as e:
            logger.error(f"Error processing P&L for GAAP: {e}")
            raise

    def generate_nasdaq_financial_ratios(
        self, balance_sheet: Dict[str, Any], income_statement: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Generate NASDAQ-required financial ratios and metrics."""
        try:
            logger.info("Calculating NASDAQ financial ratios")

            # Extract key figures
            total_assets = Decimal(str(balance_sheet["assets"]["total_assets"]))
            total_liabilities = Decimal(
                str(balance_sheet["liabilities"]["total_liabilities"])
            )
            total_equity = Decimal(str(balance_sheet["equity"]["total_equity"]))
            current_assets = sum(
                Decimal(str(v))
                for v in balance_sheet["assets"]["current_assets"].values()
            )
            current_liabilities = sum(
                Decimal(str(v))
                for v in balance_sheet["liabilities"]["current_liabilities"].values()
            )

            total_revenue = Decimal(str(income_statement["revenue"]["total_revenue"]))
            gross_profit = Decimal(str(income_statement["gross_profit"]))
            operating_income = Decimal(str(income_statement["operating_income"]))
            net_income = Decimal(str(income_statement["net_income"]))

            ratios = {
                "liquidity_ratios": {
                    "current_ratio": (
                        float(current_assets / current_liabilities)
                        if current_liabilities > 0
                        else 0
                    ),
                    "quick_ratio": (
                        float(
                            (
                                current_assets
                                - Decimal(
                                    str(
                                        balance_sheet["assets"]["current_assets"].get(
                                            "inventory", 0
                                        )
                                    )
                                )
                            )
                            / current_liabilities
                        )
                        if current_liabilities > 0
                        else 0
                    ),
                    "cash_ratio": (
                        float(
                            Decimal(
                                str(
                                    balance_sheet["assets"]["current_assets"].get(
                                        "cash_and_equivalents", 0
                                    )
                                )
                            )
                            / current_liabilities
                        )
                        if current_liabilities > 0
                        else 0
                    ),
                },
                "leverage_ratios": {
                    "debt_to_equity": (
                        float(total_liabilities / total_equity)
                        if total_equity > 0
                        else 0
                    ),
                    "debt_to_assets": (
                        float(total_liabilities / total_assets)
                        if total_assets > 0
                        else 0
                    ),
                    "equity_ratio": (
                        float(total_equity / total_assets) if total_assets > 0 else 0
                    ),
                },
                "profitability_ratios": {
                    "gross_margin": (
                        float(gross_profit / total_revenue * 100)
                        if total_revenue > 0
                        else 0
                    ),
                    "operating_margin": (
                        float(operating_income / total_revenue * 100)
                        if total_revenue > 0
                        else 0
                    ),
                    "net_margin": (
                        float(net_income / total_revenue * 100)
                        if total_revenue > 0
                        else 0
                    ),
                    "return_on_assets": (
                        float(net_income / total_assets * 100)
                        if total_assets > 0
                        else 0
                    ),
                    "return_on_equity": (
                        float(net_income / total_equity * 100)
                        if total_equity > 0
                        else 0
                    ),
                },
                "efficiency_ratios": {
                    "asset_turnover": (
                        float(total_revenue / total_assets) if total_assets > 0 else 0
                    ),
                    "equity_turnover": (
                        float(total_revenue / total_equity) if total_equity > 0 else 0
                    ),
                },
            }

            return ratios

        except Exception as e:
            logger.error(f"Error calculating NASDAQ financial ratios: {e}")
            raise

    def _classify_account_gaap(
        self, account_code: str
    ) -> Optional[GAAPAccountClassification]:
        """Classify account using GAAP standards."""
        if not account_code:
            return None

        # Try exact match first
        if account_code in self.gaap_mappings:
            return self.gaap_mappings[account_code]

        # Try prefix matching
        for code_prefix, classification in self.gaap_mappings.items():
            if account_code.startswith(code_prefix):
                return classification

        return None

    def _add_to_gaap_balance(
        self,
        gaap_balances: Dict[str, Any],
        classification: GAAPAccountClassification,
        account_name: str,
        balance: Decimal,
    ):
        """Add account balance to appropriate GAAP section."""

        # Asset classifications
        if classification in [
            GAAPAccountClassification.CASH_AND_EQUIVALENTS,
            GAAPAccountClassification.ACCOUNTS_RECEIVABLE,
            GAAPAccountClassification.INVENTORY,
            GAAPAccountClassification.PREPAID_EXPENSES,
            GAAPAccountClassification.OTHER_CURRENT_ASSETS,
        ]:
            gaap_balances["assets"]["current_assets"][account_name] = float(balance)

        elif classification in [
            GAAPAccountClassification.PROPERTY_PLANT_EQUIPMENT,
            GAAPAccountClassification.INTANGIBLE_ASSETS,
            GAAPAccountClassification.GOODWILL,
            GAAPAccountClassification.INVESTMENTS,
            GAAPAccountClassification.OTHER_NON_CURRENT_ASSETS,
        ]:
            gaap_balances["assets"]["non_current_assets"][account_name] = float(balance)

        # Liability classifications
        elif classification in [
            GAAPAccountClassification.ACCOUNTS_PAYABLE,
            GAAPAccountClassification.ACCRUED_LIABILITIES,
            GAAPAccountClassification.SHORT_TERM_DEBT,
            GAAPAccountClassification.CURRENT_PORTION_LONG_TERM_DEBT,
            GAAPAccountClassification.OTHER_CURRENT_LIABILITIES,
        ]:
            gaap_balances["liabilities"]["current_liabilities"][account_name] = float(
                balance
            )

        elif classification in [
            GAAPAccountClassification.LONG_TERM_DEBT,
            GAAPAccountClassification.DEFERRED_TAX_LIABILITIES,
            GAAPAccountClassification.OTHER_NON_CURRENT_LIABILITIES,
        ]:
            gaap_balances["liabilities"]["non_current_liabilities"][account_name] = (
                float(balance)
            )

        # Equity classifications
        elif classification in [
            GAAPAccountClassification.COMMON_STOCK,
            GAAPAccountClassification.RETAINED_EARNINGS,
            GAAPAccountClassification.ADDITIONAL_PAID_IN_CAPITAL,
            GAAPAccountClassification.ACCUMULATED_OTHER_COMPREHENSIVE_INCOME,
        ]:
            gaap_balances["equity"]["stockholders_equity"][account_name] = float(
                balance
            )

    def _calculate_gaap_totals(self, gaap_balances: Dict[str, Any]):
        """Calculate totals for GAAP balance sheet."""
        # Calculate asset totals
        current_assets_total = sum(gaap_balances["assets"]["current_assets"].values())
        non_current_assets_total = sum(
            gaap_balances["assets"]["non_current_assets"].values()
        )
        gaap_balances["assets"]["total_assets"] = (
            current_assets_total + non_current_assets_total
        )

        # Calculate liability totals
        current_liabilities_total = sum(
            gaap_balances["liabilities"]["current_liabilities"].values()
        )
        non_current_liabilities_total = sum(
            gaap_balances["liabilities"]["non_current_liabilities"].values()
        )
        gaap_balances["liabilities"]["total_liabilities"] = (
            current_liabilities_total + non_current_liabilities_total
        )

        # Calculate equity total
        equity_total = sum(gaap_balances["equity"]["stockholders_equity"].values())
        gaap_balances["equity"]["total_equity"] = equity_total

    def _validate_balance_sheet_equation(self, gaap_balances: Dict[str, Any]):
        """Validate that Assets = Liabilities + Equity (fundamental accounting equation)."""
        total_assets = gaap_balances["assets"]["total_assets"]
        total_liabilities = gaap_balances["liabilities"]["total_liabilities"]
        total_equity = gaap_balances["equity"]["total_equity"]

        difference = abs(total_assets - (total_liabilities + total_equity))

        # Allow for small rounding differences (less than $1)
        if difference > 1.00:
            logger.warning(
                f"Balance sheet equation validation failed. Difference: ${difference:.2f}"
            )
            logger.warning(
                f"Assets: ${total_assets:.2f}, Liabilities + Equity: ${total_liabilities + total_equity:.2f}"
            )
        else:
            logger.info("Balance sheet equation validated successfully")

    def _convert_decimals_to_float(self, data: Any):
        """Recursively convert Decimal objects to float for JSON serialization."""
        if isinstance(data, dict):
            for key, value in data.items():
                data[key] = self._convert_decimals_to_float(value)
        elif isinstance(data, list):
            for i, item in enumerate(data):
                data[i] = self._convert_decimals_to_float(item)
        elif isinstance(data, Decimal):
            return float(data)
        return data

    def process_multi_currency_data(
        self,
        financial_data: Dict[str, Any],
        target_currency: Optional[str] = None
    ) -> Dict[str, Any]:
        """Process financial data with multi-currency support."""
        try:
            if target_currency is None:
                target_currency = self.base_currency

            logger.info(f"Processing multi-currency data, target currency: {target_currency}")

            # Detect source currency
            source_currency = financial_data.get("currency", "USD")

            # Convert if necessary
            if source_currency != target_currency:
                logger.info(f"Converting from {source_currency} to {target_currency}")
                converted_data = self.currency_converter.convert_financial_data(
                    financial_data, target_currency, source_currency
                )
                return converted_data

            return financial_data

        except Exception as e:
            logger.error(f"Error processing multi-currency data: {e}")
            return financial_data

    def convert_transaction_amounts(
        self,
        transactions: List[Dict[str, Any]],
        target_currency: str
    ) -> List[Dict[str, Any]]:
        """Convert transaction amounts to target currency."""
        try:
            converted_transactions = []

            for transaction in transactions:
                converted_txn = transaction.copy()

                # Get transaction currency
                txn_currency = transaction.get("currency", self.base_currency)

                if txn_currency != target_currency:
                    # Convert amount
                    amount = Decimal(str(transaction.get("amount", 0)))
                    conversion = self.currency_converter.convert_amount(
                        amount, txn_currency, target_currency
                    )

                    if conversion:
                        converted_txn["amount"] = float(conversion.converted_amount)
                        converted_txn["original_amount"] = float(conversion.original_amount)
                        converted_txn["original_currency"] = conversion.from_currency
                        converted_txn["currency"] = conversion.to_currency
                        converted_txn["exchange_rate"] = float(conversion.exchange_rate)
                        converted_txn["conversion_date"] = conversion.conversion_date.isoformat()
                    else:
                        logger.warning(f"Could not convert transaction {transaction.get('id', 'unknown')}")

                converted_transactions.append(converted_txn)

            return converted_transactions

        except Exception as e:
            logger.error(f"Error converting transaction amounts: {e}")
            return transactions

    def normalize_currency_data(
        self,
        data: Dict[str, Any],
        target_currency: str = "USD"
    ) -> Dict[str, Any]:
        """Normalize all currency data to a single currency for consistent reporting."""
        try:
            logger.info(f"Normalizing currency data to {target_currency}")

            normalized_data = data.copy()

            # Process different data types
            if "transactions" in data:
                normalized_data["transactions"] = self.convert_transaction_amounts(
                    data["transactions"], target_currency
                )

            if "invoices" in data:
                normalized_data["invoices"] = self._convert_invoice_amounts(
                    data["invoices"], target_currency
                )

            if "balance_sheet" in data:
                normalized_data["balance_sheet"] = self.currency_converter.convert_financial_data(
                    data["balance_sheet"], target_currency
                )

            if "income_statement" in data:
                normalized_data["income_statement"] = self.currency_converter.convert_financial_data(
                    data["income_statement"], target_currency
                )

            if "cash_flow" in data:
                normalized_data["cash_flow"] = self.currency_converter.convert_financial_data(
                    data["cash_flow"], target_currency
                )

            # Add normalization metadata
            normalized_data["currency_normalization"] = {
                "target_currency": target_currency,
                "normalization_date": datetime.utcnow().isoformat(),
                "processor_version": "2.0"
            }

            return normalized_data

        except Exception as e:
            logger.error(f"Error normalizing currency data: {e}")
            return data

    def _convert_invoice_amounts(
        self,
        invoices: List[Dict[str, Any]],
        target_currency: str
    ) -> List[Dict[str, Any]]:
        """Convert invoice amounts to target currency."""
        try:
            converted_invoices = []

            for invoice in invoices:
                converted_inv = invoice.copy()

                # Get invoice currency
                inv_currency = invoice.get("currency", self.base_currency)

                if inv_currency != target_currency:
                    # Convert total amount
                    total = Decimal(str(invoice.get("total", 0)))
                    conversion = self.currency_converter.convert_amount(
                        total, inv_currency, target_currency
                    )

                    if conversion:
                        converted_inv["total"] = float(conversion.converted_amount)
                        converted_inv["original_total"] = float(conversion.original_amount)
                        converted_inv["original_currency"] = conversion.from_currency
                        converted_inv["currency"] = conversion.to_currency
                        converted_inv["exchange_rate"] = float(conversion.exchange_rate)
                        converted_inv["conversion_date"] = conversion.conversion_date.isoformat()

                    # Convert line items if present
                    if "line_items" in invoice:
                        converted_inv["line_items"] = self._convert_line_items(
                            invoice["line_items"], inv_currency, target_currency
                        )

                converted_invoices.append(converted_inv)

            return converted_invoices

        except Exception as e:
            logger.error(f"Error converting invoice amounts: {e}")
            return invoices

    def _convert_line_items(
        self,
        line_items: List[Dict[str, Any]],
        from_currency: str,
        to_currency: str
    ) -> List[Dict[str, Any]]:
        """Convert line item amounts to target currency."""
        try:
            converted_items = []

            for item in line_items:
                converted_item = item.copy()

                # Convert unit price
                if "unit_price" in item:
                    unit_price = Decimal(str(item["unit_price"]))
                    conversion = self.currency_converter.convert_amount(
                        unit_price, from_currency, to_currency
                    )
                    if conversion:
                        converted_item["unit_price"] = float(conversion.converted_amount)

                # Convert line total
                if "line_total" in item:
                    line_total = Decimal(str(item["line_total"]))
                    conversion = self.currency_converter.convert_amount(
                        line_total, from_currency, to_currency
                    )
                    if conversion:
                        converted_item["line_total"] = float(conversion.converted_amount)

                converted_items.append(converted_item)

            return converted_items

        except Exception as e:
            logger.error(f"Error converting line items: {e}")
            return line_items

    def get_currency_summary(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Generate currency summary for multi-currency data."""
        try:
            currencies_found = set()
            currency_totals: Dict[str, Dict[str, Any]] = {}

            # Analyze transactions
            if "transactions" in data:
                for txn in data["transactions"]:
                    currency = txn.get("currency", self.base_currency)
                    currencies_found.add(currency)

                    if currency not in currency_totals:
                        currency_totals[currency] = {"count": 0, "total_amount": Decimal("0")}

                    currency_totals[currency]["count"] = currency_totals[currency]["count"] + 1
                    amount = Decimal(str(txn.get("amount", 0)))
                    currency_totals[currency]["total_amount"] = currency_totals[currency]["total_amount"] + amount

            # Analyze invoices
            if "invoices" in data:
                for inv in data["invoices"]:
                    currency = inv.get("currency", self.base_currency)
                    currencies_found.add(currency)

            return {
                "currencies_detected": list(currencies_found),
                "currency_totals": {
                    curr: {
                        "count": totals["count"],
                        "total_amount": float(totals["total_amount"])
                    }
                    for curr, totals in currency_totals.items()
                },
                "base_currency": self.base_currency,
                "conversion_rates_available": self.currency_converter.get_supported_currencies()
            }

        except Exception as e:
            logger.error(f"Error generating currency summary: {e}")
            return {"error": str(e)}

    def process_chart_of_accounts_with_advanced_mapping(
        self,
        accounts: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """Process chart of accounts with advanced industry-specific mapping."""
        try:
            logger.info(f"Processing chart of accounts with advanced mapping for {self.industry.value} industry")

            # Use advanced account mapper
            mapping_result = self.account_mapper.map_chart_of_accounts(accounts)

            # Add additional processing
            enhanced_accounts = []
            for account in mapping_result["mapped_accounts"]:
                enhanced_account = account.copy()

                # Add GAAP compliance indicators
                enhanced_account["gaap_compliant"] = enhanced_account.get("confidence_score", 0) >= 0.7

                # Add reporting category
                enhanced_account["reporting_category"] = self._determine_reporting_category(
                    enhanced_account.get("gaap_classification", ""),
                    enhanced_account.get("account_type", "")
                )

                # Add validation flags
                enhanced_account["requires_review"] = enhanced_account.get("confidence_score", 0) < 0.5

                enhanced_accounts.append(enhanced_account)

            return {
                "accounts": enhanced_accounts,
                "mapping_statistics": mapping_result["mapping_statistics"],
                "industry": self.industry.value,
                "processing_metadata": {
                    "total_accounts": len(accounts),
                    "gaap_compliant_count": sum(1 for acc in enhanced_accounts if acc.get("gaap_compliant", False)),
                    "requires_review_count": sum(1 for acc in enhanced_accounts if acc.get("requires_review", False)),
                    "industry_specific_count": mapping_result["mapping_statistics"].get("industry_specific", 0)
                }
            }

        except Exception as e:
            logger.error(f"Error processing chart of accounts with advanced mapping: {e}")
            return {"error": str(e), "accounts": accounts}

    def _determine_reporting_category(self, gaap_classification: str, account_type: str) -> str:
        """Determine financial statement reporting category."""
        try:
            if account_type == "asset":
                if "current" in gaap_classification.lower():
                    return "current_assets"
                else:
                    return "non_current_assets"
            elif account_type == "liability":
                if "current" in gaap_classification.lower():
                    return "current_liabilities"
                else:
                    return "non_current_liabilities"
            elif account_type == "equity":
                return "stockholders_equity"
            elif account_type == "revenue":
                return "revenue"
            elif account_type == "expense":
                if "cost_of" in gaap_classification.lower():
                    return "cost_of_revenue"
                else:
                    return "operating_expenses"
            else:
                return "other"
        except Exception:
            return "other"

    def validate_account_mappings(
        self,
        accounts: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """Validate account mappings and provide recommendations."""
        try:
            logger.info("Validating account mappings")

            # Use account mapper validation
            quality_metrics = self.account_mapper.validate_mapping_quality(accounts)

            # Add additional validation
            validation_results = {
                "quality_metrics": quality_metrics,
                "validation_checks": [],
                "recommendations": quality_metrics.get("recommendations", [])
            }

            # Check for missing critical accounts
            critical_accounts = self._check_critical_accounts(accounts)
            validation_results["validation_checks"].append({
                "check_name": "critical_accounts",
                "passed": len(critical_accounts["missing"]) == 0,
                "details": critical_accounts
            })

            # Check for duplicate account codes
            duplicate_check = self._check_duplicate_accounts(accounts)
            validation_results["validation_checks"].append({
                "check_name": "duplicate_accounts",
                "passed": len(duplicate_check["duplicates"]) == 0,
                "details": duplicate_check
            })

            # Check for account naming consistency
            naming_check = self._check_account_naming_consistency(accounts)
            validation_results["validation_checks"].append({
                "check_name": "naming_consistency",
                "passed": naming_check["consistency_score"] >= 0.8,
                "details": naming_check
            })

            return validation_results

        except Exception as e:
            logger.error(f"Error validating account mappings: {e}")
            return {"error": str(e)}

    def _check_critical_accounts(self, accounts: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Check for presence of critical accounts."""
        try:
            critical_patterns = {
                "cash": r"cash|checking|bank",
                "accounts_receivable": r"receivable|a/r|debtors",
                "accounts_payable": r"payable|a/p|creditors",
                "revenue": r"revenue|sales|income",
                "expenses": r"expense|cost|expenditure"
            }

            found_critical = {}
            for critical_type, pattern in critical_patterns.items():
                found_critical[critical_type] = any(
                    re.search(pattern, account.get("name", ""), re.IGNORECASE) or
                    re.search(pattern, account.get("code", ""), re.IGNORECASE)
                    for account in accounts
                )

            missing = [crit_type for crit_type, found in found_critical.items() if not found]

            return {
                "found_critical": found_critical,
                "missing": missing,
                "critical_account_coverage": (len(found_critical) - len(missing)) / len(found_critical) * 100
            }

        except Exception as e:
            logger.error(f"Error checking critical accounts: {e}")
            return {"error": str(e)}

    def _check_duplicate_accounts(self, accounts: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Check for duplicate account codes or names."""
        try:
            codes_seen: Dict[str, Dict[str, Any]] = {}
            names_seen: Dict[str, Dict[str, Any]] = {}
            duplicates: List[Dict[str, Any]] = []

            for account in accounts:
                code = account.get("code", "")
                name = account.get("name", "")

                # Check code duplicates
                if code and code in codes_seen:
                    duplicates.append({
                        "type": "code",
                        "value": code,
                        "accounts": [codes_seen[code], account]
                    })
                else:
                    codes_seen[code] = account

                # Check name duplicates (case-insensitive)
                name_lower = name.lower()
                if name_lower and name_lower in names_seen:
                    duplicates.append({
                        "type": "name",
                        "value": name,
                        "accounts": [names_seen[name_lower], account]
                    })
                else:
                    names_seen[name_lower] = account

            return {
                "duplicates": duplicates,
                "duplicate_count": len(duplicates),
                "total_accounts": len(accounts)
            }

        except Exception as e:
            logger.error(f"Error checking duplicate accounts: {e}")
            return {"error": str(e)}

    def _check_account_naming_consistency(self, accounts: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Check account naming consistency and conventions."""
        try:
            naming_issues = []
            total_accounts = len(accounts)

            for account in accounts:
                name = account.get("name", "")
                code = account.get("code", "")

                # Check for empty names
                if not name.strip():
                    naming_issues.append({
                        "account": account,
                        "issue": "empty_name",
                        "description": "Account has empty or whitespace-only name"
                    })

                # Check for very short names (less than 3 characters)
                if len(name.strip()) < 3:
                    naming_issues.append({
                        "account": account,
                        "issue": "short_name",
                        "description": "Account name is very short (less than 3 characters)"
                    })

                # Check for inconsistent capitalization
                if name != name.title() and name != name.upper() and name != name.lower():
                    naming_issues.append({
                        "account": account,
                        "issue": "inconsistent_capitalization",
                        "description": "Account name has inconsistent capitalization"
                    })

                # Check for special characters in names
                if re.search(r'[^\w\s\-\(\)&]', name):
                    naming_issues.append({
                        "account": account,
                        "issue": "special_characters",
                        "description": "Account name contains unusual special characters"
                    })

            consistency_score = max(0, (total_accounts - len(naming_issues)) / total_accounts) if total_accounts > 0 else 1

            return {
                "naming_issues": naming_issues,
                "issue_count": len(naming_issues),
                "total_accounts": total_accounts,
                "consistency_score": consistency_score
            }

        except Exception as e:
            logger.error(f"Error checking account naming consistency: {e}")
            return {"error": str(e)}

    def process_transactions_with_classification(
        self,
        transactions: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """Process transactions with intelligent classification."""
        try:
            logger.info(f"Processing {len(transactions)} transactions with classification")

            # Classify transactions
            classification_result = self.transaction_classifier.classify_transactions_batch(transactions)

            if "error" in classification_result:
                return classification_result

            classified_transactions = classification_result["classified_transactions"]

            # Add additional processing
            enhanced_transactions = []
            for transaction in classified_transactions:
                enhanced_txn = transaction.copy()

                # Add suggested account mapping based on classification
                classification = enhanced_txn.get("classification", {})
                category = classification.get("category")

                if category:
                    suggested_account = self._suggest_account_for_category(category)
                    enhanced_txn["suggested_account"] = suggested_account

                # Add validation flags
                enhanced_txn["requires_review"] = not classification.get("is_reliable", False)
                enhanced_txn["auto_categorizable"] = classification.get("confidence_score", 0) >= 0.8

                enhanced_transactions.append(enhanced_txn)

            return {
                "transactions": enhanced_transactions,
                "classification_statistics": classification_result["classification_statistics"],
                "quality_metrics": classification_result["quality_metrics"],
                "processing_metadata": {
                    "total_transactions": len(transactions),
                    "auto_categorizable_count": sum(1 for txn in enhanced_transactions if txn.get("auto_categorizable", False)),
                    "requires_review_count": sum(1 for txn in enhanced_transactions if txn.get("requires_review", False))
                }
            }

        except Exception as e:
            logger.error(f"Error processing transactions with classification: {e}")
            return {"error": str(e), "transactions": transactions}

    def detect_and_merge_duplicates(
        self,
        financial_data: Dict[str, Any],
        auto_merge_high_confidence: bool = True,
        batch_size: int = 1000
    ) -> Dict[str, Any]:
        """Detect and optionally merge duplicate entities in financial data."""
        try:
            logger.info("Starting sophisticated duplicate detection and merging")

            result = {
                "duplicates_detected": {},
                "merges_performed": {},
                "conflicts_flagged": {},
                "processing_stats": {}
            }

            # Process each entity type
            for entity_type in ['transactions', 'contacts', 'accounts']:
                if entity_type in financial_data:
                    entities = financial_data[entity_type]
                    logger.info(f"Processing {len(entities)} {entity_type} for duplicates")

                    # Detect duplicates
                    duplicates = self.duplicate_detector.detect_duplicates(
                        entities, entity_type.rstrip('s'), batch_size
                    )

                    result["duplicates_detected"][entity_type] = {
                        "total_found": len(duplicates),
                        "high_confidence": len([d for d in duplicates if d.confidence_level == ConfidenceLevel.HIGH]),
                        "medium_confidence": len([d for d in duplicates if d.confidence_level == ConfidenceLevel.MEDIUM]),
                        "low_confidence": len([d for d in duplicates if d.confidence_level == ConfidenceLevel.LOW])
                    }

                    # Auto-merge high confidence duplicates if enabled
                    merges_performed = []
                    conflicts_flagged = []

                    if auto_merge_high_confidence:
                        high_confidence_duplicates = [
                            d for d in duplicates if d.confidence_level == ConfidenceLevel.HIGH
                        ]

                        for duplicate in high_confidence_duplicates:
                            # Find the actual entities
                            entity1 = next((e for e in entities if e.get('id') == duplicate.entity1_id), None)
                            entity2 = next((e for e in entities if e.get('id') == duplicate.entity2_id), None)

                            if entity1 and entity2:
                                merge_result = self.merge_engine.merge_duplicates(duplicate, entity1, entity2)
                                if merge_result.success:
                                    merges_performed.append({
                                        "merged_id": merge_result.merged_entity_id,
                                        "source_ids": merge_result.source_entity_ids,
                                        "confidence": duplicate.confidence_score,
                                        "conflicts_resolved": len(merge_result.conflicts_resolved)
                                    })

                                    # Update the entities list
                                    entities = [e for e in entities if e.get('id') not in merge_result.source_entity_ids]
                                    entities.append(merge_result.merged_fields)
                                else:
                                    logger.error(f"Failed to merge entities: {merge_result.error_message}")

                    # Flag medium confidence duplicates for review
                    medium_confidence_duplicates = [
                        d for d in duplicates if d.confidence_level == ConfidenceLevel.MEDIUM
                    ]

                    for duplicate in medium_confidence_duplicates:
                        conflicts_flagged.append({
                            "entity_type": entity_type,
                            "entity1_id": duplicate.entity1_id,
                            "entity2_id": duplicate.entity2_id,
                            "confidence": duplicate.confidence_score,
                            "matching_fields": duplicate.matching_fields,
                            "recommendation": duplicate.merge_recommendation
                        })

                    result["merges_performed"][entity_type] = merges_performed
                    result["conflicts_flagged"][entity_type] = conflicts_flagged

                    # Update the financial data with processed entities
                    financial_data[entity_type] = entities

                    result["processing_stats"][entity_type] = {
                        "original_count": len(financial_data[entity_type]) + len(merges_performed),
                        "final_count": len(entities),
                        "duplicates_merged": len(merges_performed),
                        "conflicts_flagged": len(conflicts_flagged)
                    }

            logger.info("Duplicate detection and merging completed successfully")
            return result

        except Exception as e:
            logger.error(f"Error in duplicate detection and merging: {e}")
            return {"error": str(e)}

    def validate_duplicate_detection_quality(
        self,
        duplicates: List[Any],
        entity_type: str
    ) -> Dict[str, Any]:
        """Validate the quality of duplicate detection results."""
        try:
            if not duplicates:
                return {
                    "quality_score": 1.0,
                    "confidence_distribution": {"high": 0, "medium": 0, "low": 0},
                    "recommendations": ["No duplicates detected - good data quality"]
                }

            # Analyze confidence distribution
            confidence_counts = {
                "high": len([d for d in duplicates if d.confidence_level == ConfidenceLevel.HIGH]),
                "medium": len([d for d in duplicates if d.confidence_level == ConfidenceLevel.MEDIUM]),
                "low": len([d for d in duplicates if d.confidence_level == ConfidenceLevel.LOW])
            }

            total_duplicates = len(duplicates)

            # Calculate quality metrics
            high_confidence_ratio = confidence_counts["high"] / total_duplicates
            avg_confidence = sum(d.confidence_score for d in duplicates) / total_duplicates

            # Quality score based on confidence distribution
            quality_score = (
                high_confidence_ratio * 0.5 +  # High confidence is good
                avg_confidence * 0.3 +         # Overall confidence matters
                (1 - total_duplicates / 1000) * 0.2  # Fewer duplicates is better (normalized)
            )
            quality_score = max(0.0, min(1.0, quality_score))

            # Generate recommendations
            recommendations = []
            if high_confidence_ratio < 0.3:
                recommendations.append("Consider reviewing duplicate detection thresholds - low high-confidence ratio")
            if avg_confidence < 0.7:
                recommendations.append("Average confidence is low - may need data quality improvements")
            if total_duplicates > 100:
                recommendations.append("High number of duplicates detected - consider data source quality review")

            if not recommendations:
                recommendations.append("Duplicate detection quality looks good")

            return {
                "quality_score": quality_score,
                "confidence_distribution": confidence_counts,
                "average_confidence": avg_confidence,
                "total_duplicates": total_duplicates,
                "recommendations": recommendations
            }

        except Exception as e:
            logger.error(f"Error validating duplicate detection quality: {e}")
            return {"error": str(e)}

    def _setup_default_transformation_rules(self) -> None:
        """Setup default transformation rules for financial data processing."""
        try:
            # Email normalization rule
            email_rule = TransformationRule(
                rule_id="normalize_email",
                rule_type=TransformationRuleType.NORMALIZATION,
                name="Email Normalization",
                description="Normalize email addresses to lowercase",
                source_fields=["email", "contact_email", "email_address"],
                target_fields=["email"],
                transformation_function="normalize_email",
                priority=10
            )
            self.transformation_engine.add_transformation_rule(email_rule)

            # Phone normalization rule
            phone_rule = TransformationRule(
                rule_id="normalize_phone",
                rule_type=TransformationRuleType.NORMALIZATION,
                name="Phone Normalization",
                description="Normalize phone numbers to standard format",
                source_fields=["phone", "phone_number", "contact_phone"],
                target_fields=["phone"],
                transformation_function="normalize_phone",
                priority=10
            )
            self.transformation_engine.add_transformation_rule(phone_rule)

            # Currency standardization rule
            currency_rule = TransformationRule(
                rule_id="standardize_currency",
                rule_type=TransformationRuleType.NORMALIZATION,
                name="Currency Standardization",
                description="Standardize currency amounts to decimal format",
                source_fields=["amount", "total", "balance", "line_amount"],
                target_fields=["amount"],
                transformation_function="standardize_currency",
                priority=20
            )
            self.transformation_engine.add_transformation_rule(currency_rule)

            # Date formatting rule
            date_rule = TransformationRule(
                rule_id="format_date",
                rule_type=TransformationRuleType.NORMALIZATION,
                name="Date Formatting",
                description="Format dates to ISO standard",
                source_fields=["date", "created_date", "modified_date", "due_date"],
                target_fields=["date"],
                transformation_function="format_date",
                priority=15
            )
            self.transformation_engine.add_transformation_rule(date_rule)

            # Text cleaning rule
            text_rule = TransformationRule(
                rule_id="clean_text",
                rule_type=TransformationRuleType.NORMALIZATION,
                name="Text Cleaning",
                description="Clean and normalize text fields",
                source_fields=["description", "name", "reference", "notes"],
                target_fields=["description"],
                transformation_function="clean_text",
                priority=5
            )
            self.transformation_engine.add_transformation_rule(text_rule)

            logger.info("Default transformation rules initialized successfully")

        except Exception as e:
            logger.error(f"Error setting up default transformation rules: {e}")

    def process_with_enhanced_pipeline(
        self,
        financial_data: Dict[str, Any],
        enable_transformations: bool = True,
        enable_quality_scoring: bool = True,
        enable_duplicate_detection: bool = True,
        batch_size: int = 1000,
        parallel_processing: bool = True
    ) -> Dict[str, Any]:
        """Process financial data through enhanced pipeline with transformations, quality scoring, and duplicate detection."""
        try:
            logger.info("Starting enhanced data processing pipeline")

            pipeline_result = {
                "original_data_stats": {},
                "transformation_results": {},
                "quality_reports": {},
                "duplicate_detection_results": {},
                "final_data_stats": {},
                "processing_metadata": {
                    "pipeline_version": "2.0",
                    "processing_timestamp": datetime.utcnow().isoformat(),
                    "features_enabled": {
                        "transformations": enable_transformations,
                        "quality_scoring": enable_quality_scoring,
                        "duplicate_detection": enable_duplicate_detection
                    }
                }
            }

            # Step 1: Collect original data statistics
            for data_type in ['transactions', 'contacts', 'accounts']:
                if data_type in financial_data:
                    data_list = financial_data[data_type]
                    pipeline_result["original_data_stats"][data_type] = {
                        "record_count": len(data_list),
                        "field_count": len(data_list[0].keys()) if data_list else 0
                    }

            # Step 2: Apply transformations if enabled
            if enable_transformations:
                logger.info("Applying data transformations")
                for data_type in ['transactions', 'contacts', 'accounts']:
                    if data_type in financial_data:
                        data_list = financial_data[data_type]
                        if data_list:
                            # Apply batch transformations
                            batch_result = self.transformation_engine.process_batch(
                                data_list,
                                data_type.rstrip('s'),  # Remove plural
                                batch_size=batch_size,
                                parallel=parallel_processing
                            )
                            pipeline_result["transformation_results"][data_type] = {
                                "batch_id": batch_result.batch_id,
                                "status": batch_result.status.value,
                                "processed_records": batch_result.processed_records,
                                "successful_records": batch_result.successful_records,
                                "failed_records": batch_result.failed_records,
                                "processing_time": batch_result.processing_time,
                                "quality_score": batch_result.overall_quality_score
                            }

            # Step 3: Generate quality reports if enabled
            if enable_quality_scoring:
                logger.info("Generating data quality reports")
                for data_type in ['transactions', 'contacts', 'accounts']:
                    if data_type in financial_data:
                        data_list = financial_data[data_type]
                        if data_list:
                            quality_report = self.quality_scorer.calculate_quality_score(data_list)
                            pipeline_result["quality_reports"][data_type] = quality_report

            # Step 4: Detect and merge duplicates if enabled
            if enable_duplicate_detection:
                logger.info("Detecting and merging duplicates")
                duplicate_results = self.detect_and_merge_duplicates(
                    financial_data,
                    auto_merge_high_confidence=True,
                    batch_size=batch_size
                )
                pipeline_result["duplicate_detection_results"] = duplicate_results

            # Step 5: Collect final data statistics
            for data_type in ['transactions', 'contacts', 'accounts']:
                if data_type in financial_data:
                    data_list = financial_data[data_type]
                    pipeline_result["final_data_stats"][data_type] = {
                        "record_count": len(data_list),
                        "field_count": len(data_list[0].keys()) if data_list else 0
                    }

            # Calculate overall pipeline metrics
            total_original_records = sum(
                stats.get("record_count", 0)
                for stats in pipeline_result["original_data_stats"].values()
            )
            total_final_records = sum(
                stats.get("record_count", 0)
                for stats in pipeline_result["final_data_stats"].values()
            )

            pipeline_result["processing_metadata"]["overall_metrics"] = {
                "total_original_records": total_original_records,
                "total_final_records": total_final_records,
                "records_processed": total_original_records,
                "data_reduction_ratio": (total_original_records - total_final_records) / total_original_records if total_original_records > 0 else 0.0
            }

            logger.info(f"Enhanced pipeline processing completed. "
                       f"Processed {total_original_records} records, "
                       f"final count: {total_final_records}")

            return pipeline_result

        except Exception as e:
            logger.error(f"Error in enhanced data processing pipeline: {e}")
            return {
                "error": str(e),
                "processing_metadata": {
                    "pipeline_version": "2.0",
                    "processing_timestamp": datetime.utcnow().isoformat(),
                    "status": "failed"
                }
            }

    def _suggest_account_for_category(self, category: str) -> Optional[str]:
        """Suggest account mapping based on transaction category."""
        try:
            # Mapping from transaction categories to typical account types
            category_to_account = {
                "product_sales": "Product Revenue",
                "service_revenue": "Service Revenue",
                "subscription_revenue": "Subscription Revenue",
                "interest_income": "Interest Income",
                "payroll_expenses": "Payroll Expenses",
                "rent_utilities": "Rent and Utilities",
                "marketing_advertising": "Marketing Expenses",
                "professional_services": "Professional Services",
                "travel_entertainment": "Travel and Entertainment",
                "office_supplies": "Office Supplies",
                "insurance": "Insurance Expense",
                "taxes_fees": "Taxes and Fees",
                "equipment_purchase": "Equipment",
                "loan_payment": "Loan Payments",
                "customer_payment": "Accounts Receivable",
                "vendor_payment": "Accounts Payable"
            }

            return category_to_account.get(category)

        except Exception as e:
            logger.error(f"Error suggesting account for category {category}: {e}")
            return None

    def process_contacts_with_enrichment(
        self,
        contacts: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """Process contacts with business intelligence enrichment."""
        try:
            logger.info(f"Processing {len(contacts)} contacts with enrichment")

            enriched_contacts = []
            enrichment_stats: Dict[str, Any] = {
                "total_contacts": len(contacts),
                "enriched_count": 0,
                "industry_classifications": {},
                "business_sizes": {},
                "geographic_regions": {},
                "average_confidence": 0.0
            }

            total_confidence = 0.0

            for contact in contacts:
                try:
                    # Enrich contact data
                    enriched_contact = self.data_enrichment_engine.enrich_contact_data(contact)

                    # Update statistics
                    if "business_profile" in enriched_contact:
                        enrichment_stats["enriched_count"] += 1

                        business_profile = enriched_contact["business_profile"]

                        # Count industry classifications
                        industry = business_profile.get("industry_classification", "unknown")
                        if industry not in enrichment_stats["industry_classifications"]:
                            enrichment_stats["industry_classifications"][industry] = 0
                        enrichment_stats["industry_classifications"][industry] += 1

                        # Count business sizes
                        size = business_profile.get("business_size", "unknown")
                        if size not in enrichment_stats["business_sizes"]:
                            enrichment_stats["business_sizes"][size] = 0
                        enrichment_stats["business_sizes"][size] += 1

                        # Count geographic regions
                        region = business_profile.get("geographic_region", "unknown")
                        if region not in enrichment_stats["geographic_regions"]:
                            enrichment_stats["geographic_regions"][region] = 0
                        enrichment_stats["geographic_regions"][region] += 1

                        # Add to confidence total
                        confidence = enriched_contact.get("enrichment_metadata", {}).get("confidence_score", 0)
                        total_confidence += confidence

                    enriched_contacts.append(enriched_contact)

                except Exception as e:
                    logger.error(f"Error enriching contact {contact.get('id', 'unknown')}: {e}")
                    enriched_contacts.append(contact)

            # Calculate average confidence
            if enrichment_stats["enriched_count"] > 0:
                enrichment_stats["average_confidence"] = total_confidence / enrichment_stats["enriched_count"]

            return {
                "contacts": enriched_contacts,
                "enrichment_statistics": enrichment_stats,
                "processing_metadata": {
                    "enrichment_rate": (enrichment_stats["enriched_count"] / len(contacts) * 100) if contacts else 0,
                    "processing_date": datetime.utcnow().isoformat()
                }
            }

        except Exception as e:
            logger.error(f"Error processing contacts with enrichment: {e}")
            return {"error": str(e), "contacts": contacts}

    def process_comprehensive_data_enrichment(
        self,
        financial_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Process comprehensive financial data with full enrichment pipeline."""
        try:
            logger.info("Processing comprehensive data enrichment")

            enriched_data = financial_data.copy()
            enrichment_summary = {
                "contacts_enriched": 0,
                "transactions_enriched": 0,
                "accounts_enriched": 0,
                "overall_confidence": 0.0,
                "enrichment_date": datetime.utcnow().isoformat()
            }

            # Enrich contacts first (needed for transaction enrichment)
            if "contacts" in financial_data:
                contact_result = self.process_contacts_with_enrichment(financial_data["contacts"])
                if "error" not in contact_result:
                    enriched_data["contacts"] = contact_result["contacts"]
                    enrichment_summary["contacts_enriched"] = contact_result["enrichment_statistics"]["enriched_count"]

            # Create contact lookup for transaction enrichment
            contact_lookup = {}
            if "contacts" in enriched_data:
                for contact in enriched_data["contacts"]:
                    contact_id = contact.get("id")
                    if contact_id:
                        contact_lookup[contact_id] = contact

            # Enrich transactions with contact context
            if "transactions" in financial_data:
                enriched_transactions = []
                for transaction in financial_data["transactions"]:
                    contact_id = transaction.get("contact_id")
                    contact_data = contact_lookup.get(contact_id) if contact_id else None

                    enriched_transaction = self.data_enrichment_engine.enrich_transaction_data(
                        transaction, contact_data
                    )
                    enriched_transactions.append(enriched_transaction)

                enriched_data["transactions"] = enriched_transactions
                enrichment_summary["transactions_enriched"] = len(enriched_transactions)

            # Enrich accounts
            if "accounts" in financial_data:
                enriched_accounts = []
                for account in financial_data["accounts"]:
                    enriched_account = self.data_enrichment_engine.enrich_account_data(account)
                    enriched_accounts.append(enriched_account)

                enriched_data["accounts"] = enriched_accounts
                enrichment_summary["accounts_enriched"] = len(enriched_accounts)

            # Calculate overall confidence
            confidence_scores = []
            for data_type in ["contacts", "transactions", "accounts"]:
                if data_type in enriched_data:
                    for item in enriched_data[data_type]:
                        metadata = item.get("enrichment_metadata", {})
                        confidence = metadata.get("confidence_score", 0)
                        if confidence > 0:
                            confidence_scores.append(confidence)

            if confidence_scores:
                enrichment_summary["overall_confidence"] = sum(confidence_scores) / len(confidence_scores)

            # Add enrichment summary to data
            enriched_data["enrichment_summary"] = enrichment_summary

            return enriched_data

        except Exception as e:
            logger.error(f"Error in comprehensive data enrichment: {e}")
            return {"error": str(e), **financial_data}

    async def process_with_integrated_validation(
        self,
        financial_data: Dict[str, Any],
        organization_id: str,
        enable_real_time_validation: bool = False,
        enable_transformation_validation: bool = True,
        enable_sync_validation: bool = True,
        batch_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """Process financial data with integrated validation at each step."""
        try:
            logger.info("Starting integrated validation processing pipeline")

            pipeline_result = {
                "ingestion_validation": {},
                "transformation_validation": {},
                "sync_validation": {},
                "final_routing": {},
                "processing_metadata": {
                    "pipeline_version": "3.0_integrated_validation",
                    "processing_timestamp": datetime.utcnow().isoformat(),
                    "organization_id": organization_id,
                    "batch_id": batch_id or f"batch_{int(time.time())}"
                }
            }

            # Step 1: Ingestion validation
            logger.info("Step 1: Performing ingestion validation")
            ingestion_context = ValidationContext(
                organization_id=organization_id,
                data_type="mixed",  # Multiple data types
                trigger=ValidationTrigger.REAL_TIME if enable_real_time_validation else ValidationTrigger.ON_INGESTION,
                batch_id=pipeline_result["processing_metadata"]["batch_id"],
                source_system="data_processor",
                metadata={"step": "ingestion", "record_count": sum(len(v) if isinstance(v, list) else 1 for v in financial_data.values())}
            )

            ingestion_result = await self.integrated_validation.validate_and_route_data(
                financial_data, ingestion_context, enable_routing=True
            )

            pipeline_result["ingestion_validation"] = {
                "action": ingestion_result.action.value,
                "route": ingestion_result.route.value,
                "policy_applied": ingestion_result.policy_applied,
                "validation_passed": ingestion_result.validation_report.passed_checks,
                "validation_failed": ingestion_result.validation_report.failed_checks,
                "processing_time": ingestion_result.processing_time
            }

            # Check if data should proceed based on ingestion validation
            if ingestion_result.action == ValidationAction.REJECT:
                logger.warning("Data rejected at ingestion validation step")
                pipeline_result["final_status"] = "rejected_at_ingestion"
                return pipeline_result

            # Step 2: Apply transformations with validation
            if enable_transformation_validation:
                logger.info("Step 2: Applying transformations with validation")

                # Store original data for comparison
                original_data = {k: v.copy() if isinstance(v, list) else v for k, v in financial_data.items()}

                # Apply transformations
                enhanced_result = self.process_with_enhanced_pipeline(
                    financial_data,
                    enable_transformations=True,
                    enable_quality_scoring=True,
                    enable_duplicate_detection=True,
                    batch_size=1000,
                    parallel_processing=True
                )

                # Validate transformed data
                transform_context = ValidationContext(
                    organization_id=organization_id,
                    data_type="mixed",
                    trigger=ValidationTrigger.ON_TRANSFORMATION,
                    batch_id=pipeline_result["processing_metadata"]["batch_id"],
                    source_system="transformation_engine",
                    metadata={"step": "transformation", "enhanced_result": enhanced_result}
                )

                transformation_result = self.integrated_validation.validate_during_transformation(
                    original_data, financial_data, transform_context
                )

                pipeline_result["transformation_validation"] = {
                    "action": transformation_result.action.value,
                    "route": transformation_result.route.value,
                    "policy_applied": transformation_result.policy_applied,
                    "validation_passed": transformation_result.validation_report.passed_checks,
                    "validation_failed": transformation_result.validation_report.failed_checks,
                    "processing_time": transformation_result.processing_time,
                    "enhancement_results": enhanced_result
                }

                # Check if data should proceed
                if transformation_result.action == ValidationAction.REJECT:
                    logger.warning("Data rejected at transformation validation step")
                    pipeline_result["final_status"] = "rejected_at_transformation"
                    return pipeline_result

            # Step 3: Sync validation (if enabled)
            if enable_sync_validation:
                logger.info("Step 3: Performing sync validation")

                sync_context = ValidationContext(
                    organization_id=organization_id,
                    data_type="mixed",
                    trigger=ValidationTrigger.ON_SYNC,
                    batch_id=pipeline_result["processing_metadata"]["batch_id"],
                    source_system="xero_sync",
                    metadata={"step": "sync_preparation"}
                )

                sync_result = self.integrated_validation.validate_during_sync(
                    financial_data, sync_context
                )

                pipeline_result["sync_validation"] = {
                    "action": sync_result.action.value,
                    "route": sync_result.route.value,
                    "policy_applied": sync_result.policy_applied,
                    "validation_passed": sync_result.validation_report.passed_checks,
                    "validation_failed": sync_result.validation_report.failed_checks,
                    "processing_time": sync_result.processing_time
                }

                # Final routing decision
                pipeline_result["final_routing"] = {
                    "final_action": sync_result.action.value,
                    "final_route": sync_result.route.value,
                    "ready_for_sync": sync_result.action in [ValidationAction.ACCEPT, ValidationAction.QUARANTINE]
                }

            # Step 4: Collect processing statistics
            validation_stats = self.integrated_validation.get_processing_statistics()
            pipeline_result["processing_metadata"]["validation_statistics"] = validation_stats

            # Determine final status
            if pipeline_result.get("final_routing", {}).get("ready_for_sync", False):
                pipeline_result["final_status"] = "ready_for_sync"
            else:
                pipeline_result["final_status"] = "requires_review"

            logger.info(f"Integrated validation pipeline completed: {pipeline_result['final_status']}")
            return pipeline_result

        except Exception as e:
            logger.error(f"Error in integrated validation processing: {e}")
            return {
                "error": str(e),
                "final_status": "processing_error",
                "processing_metadata": {
                    "pipeline_version": "3.0_integrated_validation",
                    "processing_timestamp": datetime.utcnow().isoformat(),
                    "organization_id": organization_id,
                    "error_occurred": True
                }
            }

    def validate_real_time_data(
        self,
        record: Dict[str, Any],
        data_type: str,
        organization_id: str
    ) -> Dict[str, Any]:
        """Validate a single record in real-time."""
        try:
            context = ValidationContext(
                organization_id=organization_id,
                data_type=data_type,
                trigger=ValidationTrigger.REAL_TIME,
                source_system="real_time_stream",
                metadata={"real_time_validation": True}
            )

            # Use asyncio to run the async validation
            import asyncio

            async def run_validation():
                return await self.integrated_validation.validate_and_route_data(
                    {data_type + 's': [record]}, context, enable_routing=True
                )

            # Run the async function
            try:
                loop = asyncio.get_event_loop()
            except RuntimeError:
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)

            result = loop.run_until_complete(run_validation())

            return {
                "validation_passed": result.validation_report.passed_checks > 0,
                "action": result.action.value,
                "route": result.route.value,
                "policy_applied": result.policy_applied,
                "processing_time": result.processing_time,
                "validation_details": {
                    "total_checks": result.validation_report.total_checks,
                    "passed_checks": result.validation_report.passed_checks,
                    "failed_checks": result.validation_report.failed_checks
                }
            }

        except Exception as e:
            logger.error(f"Error in real-time validation: {e}")
            return {
                "validation_passed": False,
                "action": ValidationAction.REJECT.value,
                "route": DataRoute.INVALID_QUEUE.value,
                "error": str(e)
            }
