"""
Comprehensive multiples-based valuation module for comparable company analysis.
"""

from typing import Dict, Any, List, Optional, Tuple
from decimal import Decimal, ROUND_HALF_UP
from datetime import datetime
import statistics
import logging

logger = logging.getLogger(__name__)


class MultiplesValuation:
    """Comprehensive multiples-based valuation with industry benchmarking."""

    def __init__(self):
        self.precision = Decimal("0.01")

    def _round_currency(self, value) -> Decimal:
        """Round currency values to 2 decimal places."""
        if isinstance(value, Decimal):
            return value.quantize(self.precision, rounding=ROUND_HALF_UP)
        return Decimal(str(value)).quantize(self.precision, rounding=ROUND_HALF_UP)

    def calculate_comprehensive_multiples_valuation(
        self,
        target_metrics: Dict[str, float],
        comparable_companies: List[Dict[str, Any]],
        valuation_multiples: Optional[List[str]] = None,
        weights: Optional[Dict[str, float]] = None,
    ) -> Dict[str, Any]:
        """Calculate comprehensive multiples-based valuation."""
        try:
            # Default multiples to use
            if valuation_multiples is None:
                valuation_multiples = [
                    "ev_revenue", "ev_ebitda", "pe_ratio", "peg_ratio",
                    "price_to_book", "ev_sales", "price_to_sales"
                ]

            # Default weights (equal weighting)
            if weights is None:
                weights = {multiple: 1.0 / len(valuation_multiples) for multiple in valuation_multiples}

            # Calculate industry multiples statistics
            industry_stats = self._calculate_industry_statistics(comparable_companies, valuation_multiples)

            # Calculate valuations for each multiple
            multiple_valuations = {}
            for multiple in valuation_multiples:
                if multiple in target_metrics and multiple in industry_stats:
                    valuation_result = self._calculate_single_multiple_valuation(
                        target_metrics, industry_stats[multiple], multiple
                    )
                    multiple_valuations[multiple] = valuation_result

            # Calculate weighted average valuation
            weighted_valuation = self._calculate_weighted_valuation(multiple_valuations, weights)

            # Perform quality adjustments
            quality_adjusted_valuation = self._apply_quality_adjustments(
                weighted_valuation, target_metrics, industry_stats
            )

            return {
                "valuation_date": datetime.now().isoformat(),
                "methodology": "Multiples-Based Valuation",
                "target_metrics": target_metrics,
                "industry_statistics": industry_stats,
                "individual_valuations": multiple_valuations,
                "weighted_valuation": weighted_valuation,
                "quality_adjusted_valuation": quality_adjusted_valuation,
                "comparable_companies_count": len(comparable_companies),
                "valuation_summary": self._generate_valuation_summary(
                    multiple_valuations, weighted_valuation, quality_adjusted_valuation
                ),
            }

        except Exception as e:
            logger.error(f"Error in comprehensive multiples valuation: {e}")
            raise

    def _calculate_industry_statistics(
        self, comparable_companies: List[Dict[str, Any]], multiples: List[str]
    ) -> Dict[str, Dict[str, float]]:
        """Calculate industry statistics for each multiple."""
        try:
            industry_stats = {}

            for multiple in multiples:
                values = []
                for company in comparable_companies:
                    if multiple in company and company[multiple] is not None:
                        try:
                            value = float(company[multiple])
                            if value > 0:  # Only include positive multiples
                                values.append(value)
                        except (ValueError, TypeError):
                            continue

                if values:
                    # Remove outliers (values beyond 2 standard deviations)
                    values = self._remove_outliers(values)

                    if values:  # Check if we still have values after outlier removal
                        industry_stats[multiple] = {
                            "mean": statistics.mean(values),
                            "median": statistics.median(values),
                            "min": min(values),
                            "max": max(values),
                            "std_dev": statistics.stdev(values) if len(values) > 1 else 0,
                            "count": len(values),
                            "percentile_25": self._percentile(values, 0.25),
                            "percentile_75": self._percentile(values, 0.75),
                        }

            return industry_stats

        except Exception as e:
            logger.error(f"Error calculating industry statistics: {e}")
            return {}

    def _remove_outliers(self, values: List[float], z_threshold: float = 2.0) -> List[float]:
        """Remove outliers using z-score method."""
        try:
            if len(values) < 3:
                return values

            # Convert to float to ensure numeric operations work
            numeric_values = [float(v) for v in values if v is not None]

            if len(numeric_values) < 3:
                return numeric_values

            mean_val = sum(numeric_values) / len(numeric_values)

            # Calculate standard deviation manually to avoid import issues
            variance = sum((x - mean_val) ** 2 for x in numeric_values) / len(numeric_values)
            std_dev = variance ** 0.5

            if std_dev == 0:
                return numeric_values

            filtered_values = []
            for value in numeric_values:
                z_score = abs((value - mean_val) / std_dev)
                if z_score <= z_threshold:
                    filtered_values.append(value)

            return filtered_values if filtered_values else numeric_values

        except Exception as e:
            logger.error(f"Error removing outliers: {e}")
            return values

    def _percentile(self, values: List[float], percentile: float) -> float:
        """Calculate percentile of a list of values."""
        try:
            sorted_values = sorted(values)
            index = percentile * (len(sorted_values) - 1)
            lower_index = int(index)
            upper_index = min(lower_index + 1, len(sorted_values) - 1)

            if lower_index == upper_index:
                return sorted_values[lower_index]

            weight = index - lower_index
            return sorted_values[lower_index] * (1 - weight) + sorted_values[upper_index] * weight

        except Exception as e:
            logger.error(f"Error calculating percentile: {e}")
            return 0.0

    def _calculate_single_multiple_valuation(
        self, target_metrics: Dict[str, float], industry_stats: Dict[str, float], multiple_name: str
    ) -> Dict[str, Any]:
        """Calculate valuation using a single multiple."""
        try:
            # Get the base metric for this multiple
            base_metric = self._get_base_metric_for_multiple(multiple_name)

            if base_metric not in target_metrics:
                return {"error": f"Base metric {base_metric} not found in target metrics"}

            target_value = target_metrics[base_metric]

            # Calculate valuations using different statistical measures
            valuations = {}

            for stat_name, stat_value in industry_stats.items():
                if stat_name in ["mean", "median", "percentile_25", "percentile_75"]:
                    valuation = target_value * stat_value
                    valuations[f"{stat_name}_valuation"] = float(self._round_currency(valuation))

            # Calculate confidence metrics
            confidence_score = self._calculate_confidence_score(industry_stats)

            return {
                "multiple_name": multiple_name,
                "base_metric": base_metric,
                "target_value": target_value,
                "industry_multiple": industry_stats,
                "valuations": valuations,
                "recommended_valuation": valuations.get("median_valuation", 0),
                "confidence_score": confidence_score,
            }

        except Exception as e:
            logger.error(f"Error calculating single multiple valuation: {e}")
            return {"error": str(e)}

    def _get_base_metric_for_multiple(self, multiple_name: str) -> str:
        """Get the base financial metric for a given multiple."""
        multiple_to_metric = {
            "ev_revenue": "revenue",
            "ev_ebitda": "ebitda",
            "ev_sales": "sales",
            "pe_ratio": "net_income",
            "peg_ratio": "net_income",
            "price_to_book": "book_value",
            "price_to_sales": "sales",
            "ev_fcf": "free_cash_flow",
            "price_to_fcf": "free_cash_flow",
        }
        return multiple_to_metric.get(multiple_name, "revenue")

    def _calculate_confidence_score(self, industry_stats: Dict[str, float]) -> float:
        """Calculate confidence score based on industry statistics quality."""
        try:
            count = industry_stats.get("count", 0)
            std_dev = industry_stats.get("std_dev", 0)
            mean_val = industry_stats.get("mean", 0)

            # Base score from sample size
            size_score = min(count / 10, 1.0)  # Max score at 10+ companies

            # Consistency score (lower std dev relative to mean is better)
            if mean_val > 0:
                cv = std_dev / mean_val  # Coefficient of variation
                consistency_score = max(0, 1 - cv)  # Lower CV = higher score
            else:
                consistency_score = 0

            # Combined confidence score
            confidence = (size_score * 0.6) + (consistency_score * 0.4)
            return min(max(confidence, 0), 1)  # Clamp between 0 and 1

        except Exception as e:
            logger.error(f"Error calculating confidence score: {e}")
            return 0.0

    def _calculate_weighted_valuation(
        self, multiple_valuations: Dict[str, Dict[str, Any]], weights: Dict[str, float]
    ) -> Dict[str, Any]:
        """Calculate weighted average valuation across multiples."""
        try:
            weighted_sum = 0
            total_weight = 0
            valuation_details = {}

            for multiple_name, valuation_data in multiple_valuations.items():
                if "error" not in valuation_data:
                    weight = weights.get(multiple_name, 0)
                    recommended_val = valuation_data.get("recommended_valuation", 0)
                    confidence = valuation_data.get("confidence_score", 0)

                    # Adjust weight by confidence
                    adjusted_weight = weight * confidence

                    weighted_sum += recommended_val * adjusted_weight
                    total_weight += adjusted_weight

                    valuation_details[multiple_name] = {
                        "valuation": recommended_val,
                        "weight": weight,
                        "confidence": confidence,
                        "adjusted_weight": adjusted_weight,
                    }

            final_valuation = weighted_sum / total_weight if total_weight > 0 else 0

            return {
                "weighted_valuation": float(self._round_currency(final_valuation)),
                "total_weight": total_weight,
                "valuation_details": valuation_details,
                "methodology": "Confidence-weighted average of multiple valuations",
            }

        except Exception as e:
            logger.error(f"Error calculating weighted valuation: {e}")
            return {"error": str(e)}

    def _apply_quality_adjustments(
        self,
        weighted_valuation: Dict[str, Any],
        target_metrics: Dict[str, float],
        industry_stats: Dict[str, Dict[str, float]]
    ) -> Dict[str, Any]:
        """Apply quality adjustments to the valuation."""
        try:
            base_valuation = weighted_valuation.get("weighted_valuation", 0)
            adjustments = {}

            # Growth premium/discount
            growth_adjustment = self._calculate_growth_adjustment(target_metrics, industry_stats)
            adjustments["growth_adjustment"] = growth_adjustment

            # Profitability adjustment
            profitability_adjustment = self._calculate_profitability_adjustment(target_metrics, industry_stats)
            adjustments["profitability_adjustment"] = profitability_adjustment

            # Risk adjustment
            risk_adjustment = self._calculate_risk_adjustment(target_metrics, industry_stats)
            adjustments["risk_adjustment"] = risk_adjustment

            # Apply adjustments
            total_adjustment = sum(adjustments.values())
            adjusted_valuation = base_valuation * (1 + total_adjustment)

            return {
                "base_valuation": base_valuation,
                "adjustments": adjustments,
                "total_adjustment_factor": total_adjustment,
                "quality_adjusted_valuation": float(self._round_currency(adjusted_valuation)),
                "adjustment_rationale": self._generate_adjustment_rationale(adjustments),
            }

        except Exception as e:
            logger.error(f"Error applying quality adjustments: {e}")
            return {"error": str(e)}

    def _calculate_growth_adjustment(
        self, target_metrics: Dict[str, float], industry_stats: Dict[str, Dict[str, float]]
    ) -> float:
        """Calculate growth-based valuation adjustment."""
        try:
            target_growth = target_metrics.get("revenue_growth_rate", 0)

            # Calculate industry average growth if available
            industry_growth = 0.0
            if "revenue_growth_rate" in industry_stats:
                industry_growth = industry_stats["revenue_growth_rate"].get("mean", 0.0)

            # Growth premium/discount (max ±20%)
            growth_diff = target_growth - industry_growth
            growth_adjustment = min(max(growth_diff * 0.5, -0.20), 0.20)  # 50% of growth difference, capped

            return growth_adjustment

        except Exception as e:
            logger.error(f"Error calculating growth adjustment: {e}")
            return 0.0

    def _calculate_profitability_adjustment(
        self, target_metrics: Dict[str, float], industry_stats: Dict[str, Dict[str, float]]
    ) -> float:
        """Calculate profitability-based valuation adjustment."""
        try:
            target_margin = target_metrics.get("ebitda_margin", 0)

            # Calculate industry average margin if available
            industry_margin = 0.0
            if "ebitda_margin" in industry_stats:
                industry_margin = industry_stats["ebitda_margin"].get("mean", 0.0)

            # Profitability premium/discount (max ±15%)
            margin_diff = target_margin - industry_margin
            profitability_adjustment = min(max(margin_diff * 0.3, -0.15), 0.15)  # 30% of margin difference, capped

            return profitability_adjustment

        except Exception as e:
            logger.error(f"Error calculating profitability adjustment: {e}")
            return 0.0

    def _calculate_risk_adjustment(
        self, target_metrics: Dict[str, float], industry_stats: Dict[str, Dict[str, float]]
    ) -> float:
        """Calculate risk-based valuation adjustment."""
        try:
            # Simple risk adjustment based on debt levels and volatility
            debt_to_equity = target_metrics.get("debt_to_equity", 0)

            # Higher debt = higher risk = lower valuation
            risk_adjustment = 0.0
            if debt_to_equity > 1.0:
                risk_adjustment = -min((debt_to_equity - 1.0) * 0.05, 0.10)  # Max -10% for high debt

            return risk_adjustment

        except Exception as e:
            logger.error(f"Error calculating risk adjustment: {e}")
            return 0.0

    def _generate_adjustment_rationale(self, adjustments: Dict[str, float]) -> List[str]:
        """Generate rationale for valuation adjustments."""
        rationale = []

        growth_adj = adjustments.get("growth_adjustment", 0)
        if growth_adj > 0.05:
            rationale.append("Premium applied for above-average growth prospects")
        elif growth_adj < -0.05:
            rationale.append("Discount applied for below-average growth prospects")

        prof_adj = adjustments.get("profitability_adjustment", 0)
        if prof_adj > 0.05:
            rationale.append("Premium applied for superior profitability margins")
        elif prof_adj < -0.05:
            rationale.append("Discount applied for below-average profitability")

        risk_adj = adjustments.get("risk_adjustment", 0)
        if risk_adj < -0.02:
            rationale.append("Discount applied for elevated financial risk")

        return rationale

    def _generate_valuation_summary(
        self,
        multiple_valuations: Dict[str, Dict[str, Any]],
        weighted_valuation: Dict[str, Any],
        quality_adjusted_valuation: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Generate comprehensive valuation summary."""
        try:
            # Extract key values
            individual_values = []
            for multiple_name, valuation_data in multiple_valuations.items():
                if "error" not in valuation_data:
                    individual_values.append(valuation_data.get("recommended_valuation", 0))

            weighted_val = weighted_valuation.get("weighted_valuation", 0)
            adjusted_val = quality_adjusted_valuation.get("quality_adjusted_valuation", 0)

            # Calculate statistics
            if individual_values:
                min_val = min(individual_values)
                max_val = max(individual_values)
                avg_val = sum(individual_values) / len(individual_values)

                return {
                    "valuation_range": {
                        "minimum": float(self._round_currency(min_val)),
                        "maximum": float(self._round_currency(max_val)),
                        "average": float(self._round_currency(avg_val)),
                    },
                    "weighted_average": float(self._round_currency(weighted_val)),
                    "quality_adjusted": float(self._round_currency(adjusted_val)),
                    "recommended_valuation": float(self._round_currency(adjusted_val)),
                    "valuation_confidence": self._assess_overall_confidence(multiple_valuations),
                    "key_insights": self._generate_key_insights(multiple_valuations, weighted_valuation, quality_adjusted_valuation),
                }
            else:
                return {"error": "No valid valuations calculated"}

        except Exception as e:
            logger.error(f"Error generating valuation summary: {e}")
            return {"error": str(e)}

    def _assess_overall_confidence(self, multiple_valuations: Dict[str, Dict[str, Any]]) -> str:
        """Assess overall confidence in the valuation."""
        try:
            confidence_scores = []
            for valuation_data in multiple_valuations.values():
                if "error" not in valuation_data:
                    confidence_scores.append(valuation_data.get("confidence_score", 0))

            if not confidence_scores:
                return "Low"

            avg_confidence = sum(confidence_scores) / len(confidence_scores)

            if avg_confidence >= 0.7:
                return "High"
            elif avg_confidence >= 0.4:
                return "Medium"
            else:
                return "Low"

        except Exception as e:
            logger.error(f"Error assessing confidence: {e}")
            return "Low"

    def _generate_key_insights(
        self,
        multiple_valuations: Dict[str, Dict[str, Any]],
        weighted_valuation: Dict[str, Any],
        quality_adjusted_valuation: Dict[str, Any]
    ) -> List[str]:
        """Generate key insights from the valuation analysis."""
        insights = []

        # Count valid multiples
        valid_multiples = sum(1 for v in multiple_valuations.values() if "error" not in v)
        insights.append(f"Valuation based on {valid_multiples} comparable multiples")

        # Adjustment impact
        base_val = weighted_valuation.get("weighted_valuation", 0)
        adjusted_val = quality_adjusted_valuation.get("quality_adjusted_valuation", 0)

        if base_val > 0:
            adjustment_impact = (adjusted_val - base_val) / base_val
            if adjustment_impact > 0.05:
                insights.append("Quality adjustments resulted in a valuation premium")
            elif adjustment_impact < -0.05:
                insights.append("Quality adjustments resulted in a valuation discount")

        # Range analysis
        individual_values = [v.get("recommended_valuation", 0) for v in multiple_valuations.values() if "error" not in v]
        if len(individual_values) > 1:
            val_range = max(individual_values) - min(individual_values)
            avg_val = sum(individual_values) / len(individual_values)
            if avg_val > 0:
                range_ratio = val_range / avg_val
                if range_ratio > 0.5:
                    insights.append("Wide valuation range suggests higher uncertainty")
                else:
                    insights.append("Narrow valuation range suggests good consensus")

        return insights

# Legacy function for backward compatibility
def calculate_multiples_valuation(financial_metrics, comparable_multiples):
    """Legacy function for backward compatibility."""
    try:
        valuation = 0
        for metric, value in financial_metrics.items():
            if metric in comparable_multiples:
                valuation += value * comparable_multiples[metric]
        return valuation
    except Exception as e:
        logger.error(f"Error in legacy multiples calculation: {e}")
        return 0
