"""
Discounted Cash Flow (DCF) valuation model with sensitivity analysis.
"""

from typing import Dict, Any, List, Optional
from decimal import Decimal
import numpy as np
import pandas as pd
from datetime import datetime
import logging

logger = logging.getLogger(__name__)


class DCFValuation:
    """DCF valuation model with scenario analysis."""

    def __init__(self):
        self.risk_free_rate = 0.045  # Current 10-year Treasury
        self.market_risk_premium = 0.065  # Historical equity risk premium

    def calculate_dcf_valuation(
        self,
        financial_projections: List[Dict[str, Any]],
        discount_rate: float,
        terminal_growth_rate: float,
        scenarios: Optional[List[str]] = None,
    ) -> Dict[str, Any]:
        """Calculate DCF valuation with scenario analysis."""

        try:
            scenarios = scenarios or ["base"]
            results = {}

            for scenario in scenarios:
                scenario_projections = self._adjust_projections_for_scenario(
                    financial_projections, scenario
                )

                # Calculate present value of cash flows
                pv_cash_flows = self._calculate_present_value_cash_flows(
                    scenario_projections, discount_rate
                )

                # Calculate terminal value
                terminal_value = self._calculate_terminal_value(
                    scenario_projections[-1]["free_cash_flow"],
                    discount_rate,
                    terminal_growth_rate,
                )

                # Calculate enterprise value
                enterprise_value = sum(pv_cash_flows) + terminal_value

                results[scenario] = {
                    "enterprise_value": enterprise_value,
                    "terminal_value": terminal_value,
                    "pv_cash_flows": pv_cash_flows,
                    "discount_rate": discount_rate,
                    "terminal_growth_rate": terminal_growth_rate,
                    "projections": scenario_projections,
                }

            # Add sensitivity analysis
            sensitivity_analysis = self._perform_sensitivity_analysis(
                financial_projections, discount_rate, terminal_growth_rate
            )

            return {
                "valuation_results": results,
                "sensitivity_analysis": sensitivity_analysis,
                "valuation_date": datetime.utcnow().isoformat(),
                "methodology": "Discounted Cash Flow (DCF)",
                "assumptions": {
                    "base_discount_rate": discount_rate,
                    "terminal_growth_rate": terminal_growth_rate,
                    "projection_years": len(financial_projections),
                },
            }

        except Exception as e:
            logger.error(f"Error in DCF calculation: {e}")
            raise

    def _adjust_projections_for_scenario(
        self, projections: List[Dict[str, Any]], scenario: str
    ) -> List[Dict[str, Any]]:
        """Adjust financial projections based on scenario."""
        try:
            adjusted_projections = []

            # Scenario multipliers
            scenario_adjustments = {
                "base": {"revenue_multiplier": 1.0, "expense_multiplier": 1.0, "growth_multiplier": 1.0},
                "upside": {"revenue_multiplier": 1.2, "expense_multiplier": 0.95, "growth_multiplier": 1.15},
                "downside": {"revenue_multiplier": 0.8, "expense_multiplier": 1.1, "growth_multiplier": 0.85},
                "conservative": {"revenue_multiplier": 0.9, "expense_multiplier": 1.05, "growth_multiplier": 0.9},
                "aggressive": {"revenue_multiplier": 1.3, "expense_multiplier": 0.9, "growth_multiplier": 1.25},
            }

            adjustments = scenario_adjustments.get(scenario, scenario_adjustments["base"])

            for i, projection in enumerate(projections):
                adjusted_projection = projection.copy()

                # Adjust revenue
                if "revenue" in projection:
                    adjusted_projection["revenue"] = projection["revenue"] * adjustments["revenue_multiplier"]

                # Adjust expenses
                if "operating_expenses" in projection:
                    adjusted_projection["operating_expenses"] = (
                        projection["operating_expenses"] * adjustments["expense_multiplier"]
                    )

                # Adjust growth rates for later years
                growth_factor = adjustments["growth_multiplier"] ** i
                if "free_cash_flow" in projection:
                    adjusted_projection["free_cash_flow"] = projection["free_cash_flow"] * growth_factor

                # Recalculate derived metrics
                adjusted_projection = self._recalculate_projection_metrics(adjusted_projection)
                adjusted_projections.append(adjusted_projection)

            return adjusted_projections

        except Exception as e:
            logger.error(f"Error adjusting projections for scenario {scenario}: {e}")
            raise

    def _recalculate_projection_metrics(self, projection: Dict[str, Any]) -> Dict[str, Any]:
        """Recalculate derived metrics after scenario adjustments."""
        try:
            # Basic financial calculations
            revenue = projection.get("revenue", 0)
            operating_expenses = projection.get("operating_expenses", 0)

            # Calculate EBITDA
            ebitda = revenue - operating_expenses
            projection["ebitda"] = ebitda

            # Calculate EBIT (assuming depreciation)
            depreciation = projection.get("depreciation", revenue * 0.02)  # 2% of revenue default
            ebit = ebitda - depreciation
            projection["ebit"] = ebit

            # Calculate taxes
            tax_rate = projection.get("tax_rate", 0.25)  # 25% default tax rate
            taxes = ebit * tax_rate if ebit > 0 else 0
            projection["taxes"] = taxes

            # Calculate NOPAT (Net Operating Profit After Tax)
            nopat = ebit - taxes
            projection["nopat"] = nopat

            # Calculate Free Cash Flow if not provided
            if "free_cash_flow" not in projection:
                capex = projection.get("capex", revenue * 0.03)  # 3% of revenue default
                working_capital_change = projection.get("working_capital_change", revenue * 0.01)  # 1% default

                free_cash_flow = nopat + depreciation - capex - working_capital_change
                projection["free_cash_flow"] = free_cash_flow

            return projection

        except Exception as e:
            logger.error(f"Error recalculating projection metrics: {e}")
            return projection

    def _calculate_present_value_cash_flows(
        self, projections: List[Dict[str, Any]], discount_rate: float
    ) -> List[float]:
        """Calculate present value of projected cash flows."""
        try:
            pv_cash_flows = []

            for year, projection in enumerate(projections, 1):
                cash_flow = projection.get("free_cash_flow", 0)
                present_value = cash_flow / ((1 + discount_rate) ** year)
                pv_cash_flows.append(present_value)

            return pv_cash_flows

        except Exception as e:
            logger.error(f"Error calculating present value cash flows: {e}")
            raise

    def _calculate_terminal_value(
        self, final_cash_flow: float, discount_rate: float, terminal_growth_rate: float
    ) -> float:
        """Calculate terminal value using Gordon Growth Model."""
        try:
            if discount_rate <= terminal_growth_rate:
                raise ValueError("Discount rate must be greater than terminal growth rate")

            # Terminal cash flow (next year after projection period)
            terminal_cash_flow = final_cash_flow * (1 + terminal_growth_rate)

            # Terminal value using Gordon Growth Model
            terminal_value = terminal_cash_flow / (discount_rate - terminal_growth_rate)

            # Present value of terminal value
            projection_years = len([])  # This would be passed from calling method
            # For now, assume 5 years as default
            projection_years = 5
            pv_terminal_value = terminal_value / ((1 + discount_rate) ** projection_years)

            return pv_terminal_value

        except Exception as e:
            logger.error(f"Error calculating terminal value: {e}")
            raise

    def _perform_sensitivity_analysis(
        self, projections: List[Dict[str, Any]], base_discount_rate: float, base_terminal_growth: float
    ) -> Dict[str, Any]:
        """Perform sensitivity analysis on key variables."""
        try:
            # Define sensitivity ranges
            discount_rate_range = [base_discount_rate - 0.02, base_discount_rate - 0.01,
                                 base_discount_rate, base_discount_rate + 0.01, base_discount_rate + 0.02]
            terminal_growth_range = [base_terminal_growth - 0.01, base_terminal_growth - 0.005,
                                   base_terminal_growth, base_terminal_growth + 0.005, base_terminal_growth + 0.01]

            # Create sensitivity matrix
            sensitivity_matrix = []

            for discount_rate in discount_rate_range:
                row = []
                for terminal_growth in terminal_growth_range:
                    try:
                        # Calculate valuation for this combination
                        pv_cash_flows = self._calculate_present_value_cash_flows(projections, discount_rate)
                        terminal_value = self._calculate_terminal_value(
                            projections[-1].get("free_cash_flow", 0), discount_rate, terminal_growth
                        )
                        enterprise_value = sum(pv_cash_flows) + terminal_value
                        row.append(enterprise_value)
                    except:
                        row.append(0)  # Handle edge cases
                sensitivity_matrix.append(row)

            # Calculate key statistics
            base_valuation = sensitivity_matrix[2][2]  # Middle values
            min_valuation = min(min(row) for row in sensitivity_matrix if any(val > 0 for val in row))
            max_valuation = max(max(row) for row in sensitivity_matrix)

            return {
                "sensitivity_matrix": sensitivity_matrix,
                "discount_rate_range": discount_rate_range,
                "terminal_growth_range": terminal_growth_range,
                "base_valuation": base_valuation,
                "min_valuation": min_valuation,
                "max_valuation": max_valuation,
                "valuation_range": max_valuation - min_valuation,
                "volatility": (max_valuation - min_valuation) / base_valuation if base_valuation > 0 else 0,
            }

        except Exception as e:
            logger.error(f"Error performing sensitivity analysis: {e}")
            return {}

    def calculate_wacc(
        self,
        market_value_equity: float,
        market_value_debt: float,
        cost_of_equity: float,
        cost_of_debt: float,
        tax_rate: float = 0.25,
    ) -> float:
        """Calculate Weighted Average Cost of Capital (WACC)."""
        try:
            total_value = market_value_equity + market_value_debt

            if total_value == 0:
                raise ValueError("Total market value cannot be zero")

            equity_weight = market_value_equity / total_value
            debt_weight = market_value_debt / total_value

            # WACC = (E/V * Re) + (D/V * Rd * (1 - Tc))
            wacc = (equity_weight * cost_of_equity) + (debt_weight * cost_of_debt * (1 - tax_rate))

            return wacc

        except Exception as e:
            logger.error(f"Error calculating WACC: {e}")
            raise

    def calculate_cost_of_equity_capm(
        self, risk_free_rate: float, beta: float, market_risk_premium: float
    ) -> float:
        """Calculate cost of equity using CAPM model."""
        try:
            # CAPM: Re = Rf + β(Rm - Rf)
            cost_of_equity = risk_free_rate + (beta * market_risk_premium)
            return cost_of_equity

        except Exception as e:
            logger.error(f"Error calculating cost of equity: {e}")
            raise

    def perform_monte_carlo_simulation(
        self,
        projections: List[Dict[str, Any]],
        discount_rate: float,
        terminal_growth_rate: float,
        num_simulations: int = 10000,
        volatility_assumptions: Optional[Dict[str, float]] = None,
    ) -> Dict[str, Any]:
        """Perform Monte Carlo simulation for DCF valuation."""
        try:
            import random

            # Default volatility assumptions
            if volatility_assumptions is None:
                volatility_assumptions = {
                    "revenue_volatility": 0.15,  # 15% standard deviation
                    "expense_volatility": 0.10,  # 10% standard deviation
                    "discount_rate_volatility": 0.02,  # 2% standard deviation
                    "terminal_growth_volatility": 0.005,  # 0.5% standard deviation
                }

            simulation_results = []

            for _ in range(num_simulations):
                # Generate random variations
                simulated_projections = []

                for projection in projections:
                    sim_projection = projection.copy()

                    # Add random variation to revenue
                    revenue_shock = random.gauss(0, volatility_assumptions["revenue_volatility"])
                    sim_projection["revenue"] = projection["revenue"] * (1 + revenue_shock)

                    # Add random variation to expenses
                    expense_shock = random.gauss(0, volatility_assumptions["expense_volatility"])
                    sim_projection["operating_expenses"] = projection["operating_expenses"] * (1 + expense_shock)

                    # Recalculate metrics
                    sim_projection = self._recalculate_projection_metrics(sim_projection)
                    simulated_projections.append(sim_projection)

                # Add variation to discount rate and terminal growth
                sim_discount_rate = discount_rate + random.gauss(0, volatility_assumptions["discount_rate_volatility"])
                sim_terminal_growth = terminal_growth_rate + random.gauss(0, volatility_assumptions["terminal_growth_volatility"])

                # Ensure reasonable bounds
                sim_discount_rate = max(0.01, min(0.30, sim_discount_rate))  # Between 1% and 30%
                sim_terminal_growth = max(-0.05, min(0.10, sim_terminal_growth))  # Between -5% and 10%

                # Calculate valuation for this simulation
                try:
                    pv_cash_flows = self._calculate_present_value_cash_flows(simulated_projections, sim_discount_rate)
                    terminal_value = self._calculate_terminal_value(
                        simulated_projections[-1]["free_cash_flow"], sim_discount_rate, sim_terminal_growth
                    )
                    enterprise_value = sum(pv_cash_flows) + terminal_value
                    simulation_results.append(enterprise_value)
                except:
                    continue  # Skip invalid simulations

            # Calculate statistics
            if simulation_results:
                simulation_results.sort()
                n = len(simulation_results)

                mean_valuation = sum(simulation_results) / n
                median_valuation = simulation_results[n // 2]

                # Percentiles
                p5 = simulation_results[int(n * 0.05)]
                p25 = simulation_results[int(n * 0.25)]
                p75 = simulation_results[int(n * 0.75)]
                p95 = simulation_results[int(n * 0.95)]

                # Standard deviation
                variance = sum((x - mean_valuation) ** 2 for x in simulation_results) / n
                std_dev = variance ** 0.5

                return {
                    "num_simulations": len(simulation_results),
                    "mean_valuation": mean_valuation,
                    "median_valuation": median_valuation,
                    "standard_deviation": std_dev,
                    "percentiles": {
                        "p5": p5,
                        "p25": p25,
                        "p75": p75,
                        "p95": p95,
                    },
                    "confidence_intervals": {
                        "90%": [p5, p95],
                        "50%": [p25, p75],
                    },
                    "volatility_assumptions": volatility_assumptions,
                }
            else:
                return {"error": "No valid simulations completed"}

        except Exception as e:
            logger.error(f"Error performing Monte Carlo simulation: {e}")
            return {"error": str(e)}

    def convert_enterprise_to_equity_value(
        self,
        enterprise_value: float,
        cash_and_equivalents: float = 0,
        total_debt: float = 0,
        minority_interest: float = 0,
        shares_outstanding: float = 1,
    ) -> Dict[str, float]:
        """Convert enterprise value to equity value and per-share value."""
        try:
            # Equity Value = Enterprise Value + Cash - Debt - Minority Interest
            equity_value = enterprise_value + cash_and_equivalents - total_debt - minority_interest

            # Per-share value
            value_per_share = equity_value / shares_outstanding if shares_outstanding > 0 else 0

            return {
                "enterprise_value": enterprise_value,
                "cash_and_equivalents": cash_and_equivalents,
                "total_debt": total_debt,
                "minority_interest": minority_interest,
                "equity_value": equity_value,
                "shares_outstanding": shares_outstanding,
                "value_per_share": value_per_share,
            }

        except Exception as e:
            logger.error(f"Error converting enterprise to equity value: {e}")
            raise

    def generate_comprehensive_dcf_report(
        self,
        financial_projections: List[Dict[str, Any]],
        discount_rate: float,
        terminal_growth_rate: float,
        company_info: Optional[Dict[str, Any]] = None,
        market_data: Optional[Dict[str, Any]] = None,
        include_monte_carlo: bool = True,
        monte_carlo_simulations: int = 10000,
    ) -> Dict[str, Any]:
        """Generate comprehensive DCF valuation report."""
        try:
            # Basic DCF calculation
            base_dcf = self.calculate_dcf_valuation(
                financial_projections, discount_rate, terminal_growth_rate, scenarios=["base", "upside", "downside"]
            )

            # Monte Carlo simulation if requested
            monte_carlo_results = None
            if include_monte_carlo:
                monte_carlo_results = self.perform_monte_carlo_simulation(
                    financial_projections, discount_rate, terminal_growth_rate, monte_carlo_simulations
                )

            # Calculate WACC if market data provided
            wacc_calculation = None
            if market_data:
                try:
                    wacc = self.calculate_wacc(
                        market_data.get("market_value_equity", 0),
                        market_data.get("market_value_debt", 0),
                        market_data.get("cost_of_equity", discount_rate),
                        market_data.get("cost_of_debt", 0.05),
                        market_data.get("tax_rate", 0.25),
                    )
                    wacc_calculation = {
                        "wacc": wacc,
                        "components": market_data,
                    }
                except Exception as e:
                    logger.warning(f"Could not calculate WACC: {e}")

            # Enterprise to equity conversion
            equity_conversion = None
            if market_data:
                base_enterprise_value = base_dcf["valuation_results"]["base"]["enterprise_value"]
                equity_conversion = self.convert_enterprise_to_equity_value(
                    base_enterprise_value,
                    market_data.get("cash_and_equivalents", 0),
                    market_data.get("total_debt", 0),
                    market_data.get("minority_interest", 0),
                    market_data.get("shares_outstanding", 1),
                )

            # Compile comprehensive report
            report = {
                "executive_summary": {
                    "valuation_date": datetime.now().isoformat(),
                    "company_name": company_info.get("name", "Unknown") if company_info else "Unknown",
                    "base_enterprise_value": base_dcf["valuation_results"]["base"]["enterprise_value"],
                    "equity_value": equity_conversion["equity_value"] if equity_conversion else None,
                    "value_per_share": equity_conversion["value_per_share"] if equity_conversion else None,
                    "methodology": "Discounted Cash Flow (DCF) Analysis",
                },
                "valuation_assumptions": {
                    "discount_rate": discount_rate,
                    "terminal_growth_rate": terminal_growth_rate,
                    "projection_period": len(financial_projections),
                    "wacc_calculation": wacc_calculation,
                },
                "dcf_analysis": base_dcf,
                "monte_carlo_simulation": monte_carlo_results,
                "equity_bridge": equity_conversion,
                "financial_projections": financial_projections,
                "risk_assessment": self._assess_valuation_risks(base_dcf, monte_carlo_results),
                "recommendations": self._generate_valuation_recommendations(base_dcf, monte_carlo_results),
            }

            return report

        except Exception as e:
            logger.error(f"Error generating comprehensive DCF report: {e}")
            raise

    def _assess_valuation_risks(
        self, dcf_results: Dict[str, Any], monte_carlo_results: Optional[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """Assess risks in the valuation."""
        try:
            key_risks = []
            risks = {
                "sensitivity_risk": "Medium",  # Default
                "scenario_risk": "Medium",
                "model_risk": "Low",
                "key_risks": key_risks,
            }

            # Analyze sensitivity
            sensitivity = dcf_results.get("sensitivity_analysis", {})
            if sensitivity:
                volatility = sensitivity.get("volatility", 0)
                if volatility > 0.5:
                    risks["sensitivity_risk"] = "High"
                    risks["key_risks"].append("High sensitivity to discount rate and terminal growth assumptions")
                elif volatility < 0.2:
                    risks["sensitivity_risk"] = "Low"

            # Analyze scenario differences
            valuation_results = dcf_results.get("valuation_results", {})
            if "upside" in valuation_results and "downside" in valuation_results:
                base_value = valuation_results.get("base", {}).get("enterprise_value", 0)
                upside_value = valuation_results.get("upside", {}).get("enterprise_value", 0)
                downside_value = valuation_results.get("downside", {}).get("enterprise_value", 0)

                if base_value > 0:
                    upside_diff = (upside_value - base_value) / base_value
                    downside_diff = (base_value - downside_value) / base_value

                    if max(upside_diff, downside_diff) > 0.4:
                        risks["scenario_risk"] = "High"
                        risks["key_risks"].append("Significant variation between scenarios")

            # Monte Carlo analysis
            if monte_carlo_results and "standard_deviation" in monte_carlo_results:
                mean_val = monte_carlo_results.get("mean_valuation", 0)
                std_dev = monte_carlo_results.get("standard_deviation", 0)

                if mean_val > 0:
                    coefficient_of_variation = std_dev / mean_val
                    if coefficient_of_variation > 0.3:
                        risks["key_risks"].append("High valuation uncertainty based on Monte Carlo simulation")

            return risks

        except Exception as e:
            logger.error(f"Error assessing valuation risks: {e}")
            return {"error": str(e)}

    def _generate_valuation_recommendations(
        self, dcf_results: Dict[str, Any], monte_carlo_results: Optional[Dict[str, Any]]
    ) -> List[str]:
        """Generate valuation recommendations."""
        try:
            recommendations = []

            # Base recommendations
            recommendations.append("Review and validate all financial projections with management")
            recommendations.append("Consider multiple valuation methodologies for comparison")

            # Sensitivity-based recommendations
            sensitivity = dcf_results.get("sensitivity_analysis", {})
            if sensitivity.get("volatility", 0) > 0.3:
                recommendations.append("Focus on refining discount rate and terminal growth assumptions due to high sensitivity")

            # Monte Carlo recommendations
            if monte_carlo_results:
                p5 = monte_carlo_results.get("percentiles", {}).get("p5", 0)
                p95 = monte_carlo_results.get("percentiles", {}).get("p95", 0)
                mean_val = monte_carlo_results.get("mean_valuation", 0)

                if mean_val > 0 and (p95 - p5) / mean_val > 0.5:
                    recommendations.append("Consider scenario planning due to wide valuation range in Monte Carlo analysis")

            # Scenario-based recommendations
            valuation_results = dcf_results.get("valuation_results", {})
            if len(valuation_results) > 1:
                recommendations.append("Pay attention to key value drivers that differentiate scenarios")

            return recommendations

        except Exception as e:
            logger.error(f"Error generating recommendations: {e}")
            return ["Error generating recommendations"]
