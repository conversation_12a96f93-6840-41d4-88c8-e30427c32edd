"""
Financial calculation engine for MCX3D Finance system.
Implements core financial statement generation and SaaS KPI calculations.
"""

from typing import Dict, List
from decimal import Decimal, ROUND_HALF_UP
from datetime import datetime
import pandas as pd
import logging

logger = logging.getLogger(__name__)


class FinancialCalculator:
    """Core financial calculations for statements and KPIs."""

    def __init__(self):
        self.precision = Decimal("0.01")

    def _round_currency(self, value: float) -> Decimal:
        """Round currency values to 2 decimal places."""
        return Decimal(str(value)).quantize(self.precision, rounding=ROUND_HALF_UP)

    def generate_income_statement(
        self,
        transactions_df: pd.DataFrame,
        period_start: datetime,
        period_end: datetime,
    ) -> Dict:
        """Generate income statement from transaction data."""
        try:
            # Filter transactions for period
            period_transactions = transactions_df[
                (transactions_df["date"] >= period_start)
                & (transactions_df["date"] <= period_end)
            ]

            # Revenue calculations
            revenue_accounts = period_transactions[
                period_transactions["account_type"] == "REVENUE"
            ]
            total_revenue = self._round_currency(revenue_accounts["amount"].sum())

            # Expense calculations
            expense_accounts = period_transactions[
                period_transactions["account_type"] == "EXPENSE"
            ]
            total_expenses = self._round_currency(expense_accounts["amount"].sum())

            # Cost of Goods Sold
            cogs_accounts = expense_accounts[
                expense_accounts["account_code"].str.startswith("5")
            ]
            cogs = self._round_currency(cogs_accounts["amount"].sum())

            # Operating expenses
            opex = self._round_currency(total_expenses - cogs)

            # Calculations
            gross_profit = total_revenue - cogs
            operating_income = gross_profit - opex
            net_income = operating_income  # Simplified for now

            return {
                "period_start": period_start.isoformat(),
                "period_end": period_end.isoformat(),
                "revenue": {
                    "total_revenue": float(total_revenue),
                    "breakdown": self._get_revenue_breakdown(revenue_accounts),
                },
                "expenses": {
                    "cogs": float(cogs),
                    "operating_expenses": float(opex),
                    "total_expenses": float(total_expenses),
                },
                "profitability": {
                    "gross_profit": float(gross_profit),
                    "gross_margin": (
                        float(gross_profit / total_revenue * 100)
                        if total_revenue > 0
                        else 0
                    ),
                    "operating_income": float(operating_income),
                    "operating_margin": (
                        float(operating_income / total_revenue * 100)
                        if total_revenue > 0
                        else 0
                    ),
                    "net_income": float(net_income),
                    "net_margin": (
                        float(net_income / total_revenue * 100)
                        if total_revenue > 0
                        else 0
                    ),
                },
            }
        except Exception as e:
            logger.error(f"Error generating income statement: {e}")
            raise

    def calculate_saas_kpis(
        self,
        subscription_data: pd.DataFrame,
        period_start: datetime,
        period_end: datetime,
    ) -> Dict:
        """Calculate SaaS-specific KPIs."""
        try:
            # Monthly Recurring Revenue (MRR)
            active_subscriptions = subscription_data[
                subscription_data["status"] == "active"
            ]
            mrr = self._round_currency(active_subscriptions["monthly_value"].sum())

            # Annual Recurring Revenue (ARR)
            arr = mrr * 12

            # Customer metrics
            total_customers = len(active_subscriptions)

            # Average Revenue Per User (ARPU)
            arpu = mrr / total_customers if total_customers > 0 else Decimal("0")

            # Churn calculations (simplified)
            churned_customers = len(
                subscription_data[
                    (subscription_data["churn_date"] >= period_start)
                    & (subscription_data["churn_date"] <= period_end)
                ]
            )

            churn_rate = (
                (churned_customers / total_customers * 100)
                if total_customers > 0
                else 0
            )

            return {
                "period_start": period_start.isoformat(),
                "period_end": period_end.isoformat(),
                "revenue_metrics": {
                    "mrr": float(mrr),
                    "arr": float(arr),
                    "arpu": float(arpu),
                },
                "customer_metrics": {
                    "total_customers": total_customers,
                    "churned_customers": churned_customers,
                    "churn_rate": churn_rate,
                },
            }
        except Exception as e:
            logger.error(f"Error calculating SaaS KPIs: {e}")
            raise

    def _get_revenue_breakdown(self, revenue_accounts: pd.DataFrame) -> Dict:
        """Break down revenue by account type."""
        breakdown = {}
        for account_code in revenue_accounts["account_code"].unique():
            account_total = revenue_accounts[
                revenue_accounts["account_code"] == account_code
            ]["amount"].sum()
            breakdown[account_code] = float(self._round_currency(account_total))
        return breakdown


class DCFCalculator:
    """Discounted Cash Flow valuation calculator."""

    def __init__(self, discount_rate: float = 0.10):
        self.discount_rate = discount_rate

    def calculate_dcf_valuation(
        self, cash_flows: List[float], terminal_growth_rate: float = 0.02
    ) -> Dict:
        """Calculate DCF valuation with terminal value."""
        try:
            if not cash_flows:
                raise ValueError("Cash flows list cannot be empty")

            # Present value of projected cash flows
            pv_cash_flows = []
            for i, cf in enumerate(cash_flows, 1):
                pv = cf / ((1 + self.discount_rate) ** i)
                pv_cash_flows.append(pv)

            # Terminal value calculation (Gordon Growth Model)
            terminal_cf = cash_flows[-1] * (1 + terminal_growth_rate)
            terminal_value = terminal_cf / (self.discount_rate - terminal_growth_rate)
            pv_terminal = terminal_value / ((1 + self.discount_rate) ** len(cash_flows))

            # Total enterprise value
            enterprise_value = sum(pv_cash_flows) + pv_terminal

            return {
                "projected_cash_flows": cash_flows,
                "pv_cash_flows": pv_cash_flows,
                "terminal_value": terminal_value,
                "pv_terminal_value": pv_terminal,
                "enterprise_value": enterprise_value,
                "discount_rate": self.discount_rate,
                "terminal_growth_rate": terminal_growth_rate,
            }
        except Exception as e:
            logger.error(f"Error calculating DCF valuation: {e}")
            raise
