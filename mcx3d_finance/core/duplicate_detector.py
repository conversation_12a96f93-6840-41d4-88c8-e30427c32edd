"""
Sophisticated duplicate detection and merging system for financial data.
Implements fuzzy matching, confidence scoring, and automatic merging with NASDAQ compliance.
"""

from typing import Dict, List, Any, Optional, Tuple, Set
from dataclasses import dataclass
from enum import Enum
from decimal import Decimal
import logging
import re
from datetime import datetime, timedelta
from difflib import SequenceMatcher
import hashlib

logger = logging.getLogger(__name__)


class MatchType(Enum):
    """Types of duplicate matches."""
    EXACT = "exact"
    FUZZY = "fuzzy"
    PHONETIC = "phonetic"
    SEMANTIC = "semantic"


class ConfidenceLevel(Enum):
    """Confidence levels for duplicate detection."""
    HIGH = "high"          # >= 0.9 - Auto-merge
    MEDIUM = "medium"      # 0.7-0.89 - Flag for review
    LOW = "low"           # 0.5-0.69 - Log but don't merge
    NONE = "none"         # < 0.5 - Not a duplicate


@dataclass
class DuplicateMatch:
    """Represents a potential duplicate match."""
    entity1_id: str
    entity2_id: str
    entity_type: str
    confidence_score: float
    confidence_level: ConfidenceLevel
    match_type: MatchType
    matching_fields: List[str]
    field_scores: Dict[str, float]
    merge_recommendation: str
    created_at: datetime


@dataclass
class MergeResult:
    """Result of a merge operation."""
    merged_entity_id: str
    source_entity_ids: List[str]
    merged_fields: Dict[str, Any]
    conflicts_resolved: List[Dict[str, Any]]
    audit_trail: Dict[str, Any]
    success: bool
    error_message: Optional[str] = None


class FuzzyMatcher:
    """Advanced fuzzy matching algorithms for duplicate detection."""
    
    def __init__(self):
        self.soundex_cache = {}
    
    def levenshtein_distance(self, s1: str, s2: str) -> int:
        """Calculate Levenshtein distance between two strings."""
        if len(s1) < len(s2):
            return self.levenshtein_distance(s2, s1)
        
        if len(s2) == 0:
            return len(s1)
        
        previous_row = list(range(len(s2) + 1))
        for i, c1 in enumerate(s1):
            current_row = [i + 1]
            for j, c2 in enumerate(s2):
                insertions = previous_row[j + 1] + 1
                deletions = current_row[j] + 1
                substitutions = previous_row[j] + (c1 != c2)
                current_row.append(min(insertions, deletions, substitutions))
            previous_row = current_row
        
        return previous_row[-1]
    
    def jaro_winkler_similarity(self, s1: str, s2: str) -> float:
        """Calculate Jaro-Winkler similarity (better for names)."""
        if not s1 or not s2:
            return 0.0
        
        if s1 == s2:
            return 1.0
        
        # Jaro similarity calculation
        match_distance = (max(len(s1), len(s2)) // 2) - 1
        if match_distance < 0:
            match_distance = 0
        
        s1_matches = [False] * len(s1)
        s2_matches = [False] * len(s2)
        
        matches = 0
        transpositions = 0
        
        # Find matches
        for i in range(len(s1)):
            start = max(0, i - match_distance)
            end = min(i + match_distance + 1, len(s2))
            
            for j in range(start, end):
                if s2_matches[j] or s1[i] != s2[j]:
                    continue
                s1_matches[i] = s2_matches[j] = True
                matches += 1
                break
        
        if matches == 0:
            return 0.0
        
        # Count transpositions
        k = 0
        for i in range(len(s1)):
            if not s1_matches[i]:
                continue
            while not s2_matches[k]:
                k += 1
            if s1[i] != s2[k]:
                transpositions += 1
            k += 1
        
        jaro = (matches / len(s1) + matches / len(s2) + 
                (matches - transpositions / 2) / matches) / 3
        
        # Winkler modification
        prefix = 0
        for i in range(min(len(s1), len(s2), 4)):
            if s1[i] == s2[i]:
                prefix += 1
            else:
                break
        
        return jaro + (0.1 * prefix * (1 - jaro))
    
    def soundex(self, name: str) -> str:
        """Generate Soundex code for phonetic matching."""
        if name in self.soundex_cache:
            return self.soundex_cache[name]
        
        name = name.upper()
        soundex_code = name[0] if name else ""
        
        # Soundex mapping
        mapping = {
            'BFPV': '1', 'CGJKQSXZ': '2', 'DT': '3',
            'L': '4', 'MN': '5', 'R': '6'
        }
        
        for char in name[1:]:
            for key, value in mapping.items():
                if char in key:
                    if soundex_code[-1] != value:
                        soundex_code += value
                    break
        
        # Pad with zeros and truncate to 4 characters
        soundex_code = (soundex_code + '000')[:4]
        self.soundex_cache[name] = soundex_code
        return soundex_code
    
    def token_similarity(self, s1: str, s2: str) -> float:
        """Calculate token-based similarity for descriptions."""
        tokens1 = set(re.findall(r'\w+', s1.lower()))
        tokens2 = set(re.findall(r'\w+', s2.lower()))
        
        if not tokens1 and not tokens2:
            return 1.0
        if not tokens1 or not tokens2:
            return 0.0
        
        intersection = tokens1.intersection(tokens2)
        union = tokens1.union(tokens2)
        
        return len(intersection) / len(union)
    
    def normalize_string(self, s: str) -> str:
        """Normalize string for comparison."""
        if not s:
            return ""

        # Remove extra whitespace, convert to lowercase
        s = re.sub(r'\s+', ' ', s.strip().lower())

        # Remove common business suffixes (more comprehensive pattern)
        business_pattern = r'\b(ltd|llc|inc|corp|co|company|limited|corporation)\.?\b'
        s = re.sub(business_pattern, '', s, flags=re.IGNORECASE)

        # Clean up extra spaces after suffix removal
        s = re.sub(r'\s+', ' ', s.strip())

        return s


class ConfidenceScorer:
    """Confidence scoring system for duplicate matches."""
    
    def __init__(self):
        self.field_weights = {
            'transaction': {
                'amount': 0.3,
                'date': 0.2,
                'description': 0.25,
                'account_id': 0.15,
                'reference': 0.1
            },
            'contact': {
                'name': 0.4,
                'email': 0.3,
                'phone': 0.2,
                'address': 0.1
            },
            'account': {
                'code': 0.4,
                'name': 0.35,
                'type': 0.15,
                'description': 0.1
            }
        }
    
    def calculate_confidence(
        self,
        entity_type: str,
        field_scores: Dict[str, float]
    ) -> Tuple[float, ConfidenceLevel]:
        """Calculate overall confidence score."""
        weights = self.field_weights.get(entity_type, {})
        
        if not weights:
            logger.warning(f"No weights defined for entity type: {entity_type}")
            return 0.0, ConfidenceLevel.NONE
        
        weighted_score = 0.0
        total_weight = 0.0
        
        for field, score in field_scores.items():
            weight = weights.get(field, 0.0)
            weighted_score += score * weight
            total_weight += weight
        
        if total_weight == 0:
            return 0.0, ConfidenceLevel.NONE
        
        confidence = weighted_score / total_weight
        
        # Determine confidence level
        if confidence >= 0.9:
            level = ConfidenceLevel.HIGH
        elif confidence >= 0.7:
            level = ConfidenceLevel.MEDIUM
        elif confidence >= 0.5:
            level = ConfidenceLevel.LOW
        else:
            level = ConfidenceLevel.NONE
        
        return confidence, level


class DuplicateDetector:
    """Main duplicate detection engine."""
    
    def __init__(self):
        self.fuzzy_matcher = FuzzyMatcher()
        self.confidence_scorer = ConfidenceScorer()
        self.processed_pairs: Set[Tuple[str, str]] = set()
    
    def detect_duplicates(
        self,
        entities: List[Dict[str, Any]],
        entity_type: str,
        batch_size: int = 1000
    ) -> List[DuplicateMatch]:
        """Detect duplicates in a list of entities."""
        try:
            logger.info(f"Starting duplicate detection for {len(entities)} {entity_type} entities")
            
            duplicates = []
            
            # Process in batches for performance
            for i in range(0, len(entities), batch_size):
                batch = entities[i:i + batch_size]
                batch_duplicates = self._detect_batch_duplicates(batch, entity_type)
                duplicates.extend(batch_duplicates)
                
                logger.info(f"Processed batch {i//batch_size + 1}, found {len(batch_duplicates)} duplicates")
            
            logger.info(f"Duplicate detection completed. Found {len(duplicates)} potential duplicates")
            return duplicates

        except Exception as e:
            logger.error(f"Error in duplicate detection: {e}")
            return []

    def _compare_entities(
        self,
        entity1: Dict[str, Any],
        entity2: Dict[str, Any],
        entity_type: str
    ) -> Optional[DuplicateMatch]:
        """Compare two entities and return match if duplicate detected."""
        try:
            if entity_type == 'transaction':
                return self._compare_transactions(entity1, entity2)
            elif entity_type == 'contact':
                return self._compare_contacts(entity1, entity2)
            elif entity_type == 'account':
                return self._compare_accounts(entity1, entity2)
            else:
                logger.warning(f"Unknown entity type: {entity_type}")
                return None

        except Exception as e:
            logger.error(f"Error comparing entities: {e}")
            return None

    def _compare_transactions(
        self,
        txn1: Dict[str, Any],
        txn2: Dict[str, Any]
    ) -> Optional[DuplicateMatch]:
        """Compare two transactions for duplicates."""
        field_scores = {}
        matching_fields = []

        # Amount comparison (exact match or very close)
        amount1 = Decimal(str(txn1.get('amount', 0)))
        amount2 = Decimal(str(txn2.get('amount', 0)))

        if amount1 == amount2:
            field_scores['amount'] = 1.0
            matching_fields.append('amount')
        elif amount1 != 0 and amount2 != 0:
            # Allow for small rounding differences
            diff_ratio = abs(amount1 - amount2) / max(abs(amount1), abs(amount2))
            if diff_ratio < Decimal('0.01'):  # 1% tolerance
                field_scores['amount'] = float(Decimal('1.0') - diff_ratio)
                matching_fields.append('amount')
            else:
                field_scores['amount'] = 0.0
        else:
            field_scores['amount'] = 0.0

        # Date comparison (exact or within 1 day)
        date1_str = txn1.get('date', '')
        date2_str = txn2.get('date', '')

        if date1_str == date2_str:
            field_scores['date'] = 1.0
            matching_fields.append('date')
        elif date1_str and date2_str:
            try:
                date1 = datetime.fromisoformat(date1_str.replace('Z', '+00:00'))
                date2 = datetime.fromisoformat(date2_str.replace('Z', '+00:00'))
                diff_days = abs((date1 - date2).days)

                if diff_days == 0:
                    field_scores['date'] = 1.0
                    matching_fields.append('date')
                elif diff_days <= 1:
                    field_scores['date'] = 0.8
                    matching_fields.append('date')
                else:
                    field_scores['date'] = max(0, 1.0 - (diff_days / 30))  # Decay over 30 days
            except (ValueError, TypeError):
                field_scores['date'] = 0.0
        else:
            field_scores['date'] = 0.0

        # Description comparison (fuzzy matching)
        desc1 = self.fuzzy_matcher.normalize_string(txn1.get('description', ''))
        desc2 = self.fuzzy_matcher.normalize_string(txn2.get('description', ''))

        if desc1 and desc2:
            # Use multiple similarity measures
            jaro_sim = self.fuzzy_matcher.jaro_winkler_similarity(desc1, desc2)
            token_sim = self.fuzzy_matcher.token_similarity(desc1, desc2)

            # Combine similarities with weights
            desc_similarity = (jaro_sim * 0.6) + (token_sim * 0.4)
            field_scores['description'] = desc_similarity

            if desc_similarity >= 0.8:
                matching_fields.append('description')
        else:
            field_scores['description'] = 0.0

        # Account ID comparison
        acc1 = txn1.get('account_id', '')
        acc2 = txn2.get('account_id', '')

        if acc1 == acc2 and acc1:
            field_scores['account_id'] = 1.0
            matching_fields.append('account_id')
        else:
            field_scores['account_id'] = 0.0

        # Reference comparison
        ref1 = txn1.get('reference', '')
        ref2 = txn2.get('reference', '')

        if ref1 and ref2:
            if ref1 == ref2:
                field_scores['reference'] = 1.0
                matching_fields.append('reference')
            else:
                ref_similarity = self.fuzzy_matcher.jaro_winkler_similarity(ref1, ref2)
                field_scores['reference'] = ref_similarity
                if ref_similarity >= 0.9:
                    matching_fields.append('reference')
        else:
            field_scores['reference'] = 0.0

        # Calculate overall confidence
        confidence, level = self.confidence_scorer.calculate_confidence('transaction', field_scores)

        if level == ConfidenceLevel.NONE:
            return None

        return DuplicateMatch(
            entity1_id=txn1.get('id', ''),
            entity2_id=txn2.get('id', ''),
            entity_type='transaction',
            confidence_score=confidence,
            confidence_level=level,
            match_type=MatchType.FUZZY if confidence < 1.0 else MatchType.EXACT,
            matching_fields=matching_fields,
            field_scores=field_scores,
            merge_recommendation=self._get_merge_recommendation(level),
            created_at=datetime.utcnow()
        )

    def _compare_contacts(
        self,
        contact1: Dict[str, Any],
        contact2: Dict[str, Any]
    ) -> Optional[DuplicateMatch]:
        """Compare two contacts for duplicates."""
        field_scores = {}
        matching_fields = []

        # Name comparison (fuzzy matching)
        name1 = self.fuzzy_matcher.normalize_string(contact1.get('name', ''))
        name2 = self.fuzzy_matcher.normalize_string(contact2.get('name', ''))

        if name1 and name2:
            jaro_sim = self.fuzzy_matcher.jaro_winkler_similarity(name1, name2)
            soundex1 = self.fuzzy_matcher.soundex(name1)
            soundex2 = self.fuzzy_matcher.soundex(name2)

            # Combine Jaro-Winkler and Soundex
            phonetic_bonus = 0.1 if soundex1 == soundex2 else 0.0
            name_similarity = min(1.0, jaro_sim + phonetic_bonus)

            field_scores['name'] = name_similarity
            if name_similarity >= 0.8:
                matching_fields.append('name')
        else:
            field_scores['name'] = 0.0

        # Email comparison (exact match)
        email1 = contact1.get('email', '').lower().strip()
        email2 = contact2.get('email', '').lower().strip()

        if email1 and email2:
            if email1 == email2:
                field_scores['email'] = 1.0
                matching_fields.append('email')
            else:
                field_scores['email'] = 0.0
        else:
            field_scores['email'] = 0.0

        # Phone comparison (normalized)
        phone1 = self._normalize_phone(contact1.get('phone', ''))
        phone2 = self._normalize_phone(contact2.get('phone', ''))

        if phone1 and phone2:
            if phone1 == phone2:
                field_scores['phone'] = 1.0
                matching_fields.append('phone')
            else:
                field_scores['phone'] = 0.0
        else:
            field_scores['phone'] = 0.0

        # Address comparison (fuzzy)
        addr1 = self.fuzzy_matcher.normalize_string(contact1.get('address', ''))
        addr2 = self.fuzzy_matcher.normalize_string(contact2.get('address', ''))

        if addr1 and addr2:
            addr_similarity = self.fuzzy_matcher.token_similarity(addr1, addr2)
            field_scores['address'] = addr_similarity
            if addr_similarity >= 0.7:
                matching_fields.append('address')
        else:
            field_scores['address'] = 0.0

        # Calculate overall confidence
        confidence, level = self.confidence_scorer.calculate_confidence('contact', field_scores)

        if level == ConfidenceLevel.NONE:
            return None

        return DuplicateMatch(
            entity1_id=contact1.get('id', ''),
            entity2_id=contact2.get('id', ''),
            entity_type='contact',
            confidence_score=confidence,
            confidence_level=level,
            match_type=MatchType.FUZZY if confidence < 1.0 else MatchType.EXACT,
            matching_fields=matching_fields,
            field_scores=field_scores,
            merge_recommendation=self._get_merge_recommendation(level),
            created_at=datetime.utcnow()
        )

    def _compare_accounts(
        self,
        account1: Dict[str, Any],
        account2: Dict[str, Any]
    ) -> Optional[DuplicateMatch]:
        """Compare two accounts for duplicates."""
        field_scores = {}
        matching_fields = []

        # Code comparison (exact match preferred)
        code1 = account1.get('code', '').strip()
        code2 = account2.get('code', '').strip()

        if code1 and code2:
            if code1 == code2:
                field_scores['code'] = 1.0
                matching_fields.append('code')
            else:
                # Allow for minor variations in codes
                code_similarity = self.fuzzy_matcher.jaro_winkler_similarity(code1, code2)
                field_scores['code'] = code_similarity
                if code_similarity >= 0.9:
                    matching_fields.append('code')
        else:
            field_scores['code'] = 0.0

        # Name comparison (fuzzy matching)
        name1 = self.fuzzy_matcher.normalize_string(account1.get('name', ''))
        name2 = self.fuzzy_matcher.normalize_string(account2.get('name', ''))

        if name1 and name2:
            name_similarity = self.fuzzy_matcher.jaro_winkler_similarity(name1, name2)
            field_scores['name'] = name_similarity
            if name_similarity >= 0.8:
                matching_fields.append('name')
        else:
            field_scores['name'] = 0.0

        # Type comparison (exact match)
        type1 = account1.get('type', '').lower()
        type2 = account2.get('type', '').lower()

        if type1 and type2:
            if type1 == type2:
                field_scores['type'] = 1.0
                matching_fields.append('type')
            else:
                field_scores['type'] = 0.0
        else:
            field_scores['type'] = 0.0

        # Description comparison (fuzzy)
        desc1 = self.fuzzy_matcher.normalize_string(account1.get('description', ''))
        desc2 = self.fuzzy_matcher.normalize_string(account2.get('description', ''))

        if desc1 and desc2:
            desc_similarity = self.fuzzy_matcher.token_similarity(desc1, desc2)
            field_scores['description'] = desc_similarity
            if desc_similarity >= 0.7:
                matching_fields.append('description')
        else:
            field_scores['description'] = 0.0

        # Calculate overall confidence
        confidence, level = self.confidence_scorer.calculate_confidence('account', field_scores)

        if level == ConfidenceLevel.NONE:
            return None

        return DuplicateMatch(
            entity1_id=account1.get('id', ''),
            entity2_id=account2.get('id', ''),
            entity_type='account',
            confidence_score=confidence,
            confidence_level=level,
            match_type=MatchType.FUZZY if confidence < 1.0 else MatchType.EXACT,
            matching_fields=matching_fields,
            field_scores=field_scores,
            merge_recommendation=self._get_merge_recommendation(level),
            created_at=datetime.utcnow()
        )

    def _normalize_phone(self, phone: str) -> str:
        """Normalize phone number for comparison."""
        if not phone:
            return ""

        # Remove all non-digit characters
        digits_only = re.sub(r'\D', '', phone)

        # Handle different formats
        if len(digits_only) == 10:
            # US format without country code
            return digits_only
        elif len(digits_only) == 11 and digits_only.startswith('1'):
            # US format with country code
            return digits_only[1:]
        elif len(digits_only) > 10:
            # International format - keep last 10 digits
            return digits_only[-10:]

        return digits_only

    def _get_merge_recommendation(self, confidence_level: ConfidenceLevel) -> str:
        """Get merge recommendation based on confidence level."""
        if confidence_level == ConfidenceLevel.HIGH:
            return "auto_merge"
        elif confidence_level == ConfidenceLevel.MEDIUM:
            return "review_required"
        elif confidence_level == ConfidenceLevel.LOW:
            return "manual_review"
        else:
            return "no_action"

    def _detect_batch_duplicates(
        self,
        entities: List[Dict[str, Any]],
        entity_type: str
    ) -> List[DuplicateMatch]:
        """Detect duplicates within a batch with performance optimizations."""
        duplicates = []

        # Create blocking index for performance optimization
        blocks = self._create_blocking_index(entities, entity_type)

        for block_key, block_entities in blocks.items():
            if len(block_entities) < 2:
                continue

            # Only compare within blocks to reduce O(n²) complexity
            for i in range(len(block_entities)):
                for j in range(i + 1, len(block_entities)):
                    entity1 = block_entities[i]
                    entity2 = block_entities[j]

                    # Skip if already processed
                    pair_key = tuple(sorted([entity1.get('id', ''), entity2.get('id', '')]))
                    if pair_key in self.processed_pairs:
                        continue

                    self.processed_pairs.add(pair_key)

                    # Quick pre-filter to avoid expensive comparisons
                    if not self._quick_prefilter(entity1, entity2, entity_type):
                        continue

                    # Calculate similarity
                    match = self._compare_entities(entity1, entity2, entity_type)
                    if match and match.confidence_level != ConfidenceLevel.NONE:
                        duplicates.append(match)

        return duplicates

    def _create_blocking_index(
        self,
        entities: List[Dict[str, Any]],
        entity_type: str
    ) -> Dict[str, List[Dict[str, Any]]]:
        """Create blocking index to reduce comparison space."""
        blocks = {}

        for entity in entities:
            if entity_type == 'transaction':
                # Block by date and amount range
                date_key = entity.get('date', '')[:7]  # YYYY-MM
                amount = float(entity.get('amount', 0))
                amount_bucket = int(amount // 100) * 100  # Round to nearest 100
                block_key = f"{date_key}_{amount_bucket}"
            elif entity_type == 'contact':
                # Block by first letter of name and email domain
                name = entity.get('name', '').strip()
                name_key = name[0].lower() if name else 'unknown'
                email = entity.get('email', '')
                email_domain = email.split('@')[-1] if '@' in email else 'unknown'
                block_key = f"{name_key}_{email_domain}"
            elif entity_type == 'account':
                # Block by account type and code prefix
                acc_type = entity.get('type', 'unknown').lower()
                code = entity.get('code', '')
                code_prefix = code[:2] if len(code) >= 2 else code
                block_key = f"{acc_type}_{code_prefix}"
            else:
                block_key = 'default'

            if block_key not in blocks:
                blocks[block_key] = []
            blocks[block_key].append(entity)

        return blocks

    def _quick_prefilter(
        self,
        entity1: Dict[str, Any],
        entity2: Dict[str, Any],
        entity_type: str
    ) -> bool:
        """Quick prefilter to avoid expensive comparisons."""
        if entity_type == 'transaction':
            # Quick amount check
            amount1 = float(entity1.get('amount', 0))
            amount2 = float(entity2.get('amount', 0))
            if amount1 != 0 and amount2 != 0:
                ratio = abs(amount1 - amount2) / max(abs(amount1), abs(amount2))
                if ratio > 0.1:  # More than 10% difference
                    return False

            # Quick date check
            date1 = entity1.get('date', '')
            date2 = entity2.get('date', '')
            if date1 and date2 and date1[:7] != date2[:7]:  # Different month
                return False

        elif entity_type == 'contact':
            # Quick name check
            name1 = entity1.get('name', '').lower()
            name2 = entity2.get('name', '').lower()
            if name1 and name2:
                # Check if first characters are too different
                if abs(ord(name1[0]) - ord(name2[0])) > 2:
                    return False

        elif entity_type == 'account':
            # Quick type check
            type1 = entity1.get('type', '').lower()
            type2 = entity2.get('type', '').lower()
            if type1 and type2 and type1 != type2:
                # Only allow type mismatches for very similar codes/names
                code1 = entity1.get('code', '')
                code2 = entity2.get('code', '')
                if code1 != code2:
                    return False

        return True


class MergeEngine:
    """Engine for merging duplicate entities with conflict resolution."""

    def __init__(self):
        self.merge_strategies = {
            'transaction': self._merge_transactions,
            'contact': self._merge_contacts,
            'account': self._merge_accounts
        }

    def merge_duplicates(
        self,
        duplicate_match: DuplicateMatch,
        entity1: Dict[str, Any],
        entity2: Dict[str, Any]
    ) -> MergeResult:
        """Merge two duplicate entities."""
        try:
            logger.info(f"Merging {duplicate_match.entity_type} entities: "
                       f"{duplicate_match.entity1_id} and {duplicate_match.entity2_id}")

            merge_strategy = self.merge_strategies.get(duplicate_match.entity_type)
            if not merge_strategy:
                return MergeResult(
                    merged_entity_id="",
                    source_entity_ids=[],
                    merged_fields={},
                    conflicts_resolved=[],
                    audit_trail={},
                    success=False,
                    error_message=f"No merge strategy for entity type: {duplicate_match.entity_type}"
                )

            return merge_strategy(duplicate_match, entity1, entity2)

        except Exception as e:
            logger.error(f"Error merging entities: {e}")
            return MergeResult(
                merged_entity_id="",
                source_entity_ids=[],
                merged_fields={},
                conflicts_resolved=[],
                audit_trail={},
                success=False,
                error_message=str(e)
            )

    def _merge_transactions(
        self,
        duplicate_match: DuplicateMatch,
        txn1: Dict[str, Any],
        txn2: Dict[str, Any]
    ) -> MergeResult:
        """Merge two transaction entities."""
        merged_fields = {}
        conflicts_resolved = []

        # Determine primary transaction (most recent or most complete)
        primary_txn, secondary_txn = self._select_primary_entity(txn1, txn2)

        # Merge fields with conflict resolution
        for field in ['amount', 'date', 'description', 'account_id', 'reference', 'type']:
            primary_value = primary_txn.get(field)
            secondary_value = secondary_txn.get(field)

            if primary_value and secondary_value and primary_value != secondary_value:
                # Conflict detected
                conflicts_resolved.append({
                    'field': field,
                    'primary_value': primary_value,
                    'secondary_value': secondary_value,
                    'resolution': 'primary_selected',
                    'reason': 'primary_entity_precedence'
                })
                merged_fields[field] = primary_value
            elif primary_value:
                merged_fields[field] = primary_value
            elif secondary_value:
                merged_fields[field] = secondary_value

        # Generate new merged entity ID
        merged_id = self._generate_merged_id([txn1.get('id', ''), txn2.get('id', '')])
        merged_fields['id'] = merged_id
        merged_fields['merged_from'] = [txn1.get('id', ''), txn2.get('id', '')]
        merged_fields['merge_timestamp'] = datetime.utcnow().isoformat()

        return MergeResult(
            merged_entity_id=merged_id,
            source_entity_ids=[txn1.get('id', ''), txn2.get('id', '')],
            merged_fields=merged_fields,
            conflicts_resolved=conflicts_resolved,
            audit_trail={
                'merge_confidence': duplicate_match.confidence_score,
                'matching_fields': duplicate_match.matching_fields,
                'merge_timestamp': datetime.utcnow().isoformat(),
                'merge_type': 'automatic' if duplicate_match.confidence_level == ConfidenceLevel.HIGH else 'manual'
            },
            success=True
        )

    def _merge_contacts(
        self,
        duplicate_match: DuplicateMatch,
        contact1: Dict[str, Any],
        contact2: Dict[str, Any]
    ) -> MergeResult:
        """Merge two contact entities."""
        merged_fields = {}
        conflicts_resolved = []

        primary_contact, secondary_contact = self._select_primary_entity(contact1, contact2)

        # Merge fields with specific contact logic
        for field in ['name', 'email', 'phone', 'address', 'company']:
            primary_value = primary_contact.get(field)
            secondary_value = secondary_contact.get(field)

            if field == 'email' and primary_value and secondary_value and primary_value != secondary_value:
                # For emails, keep both if different
                merged_fields['primary_email'] = primary_value
                merged_fields['secondary_email'] = secondary_value
                merged_fields[field] = primary_value  # Use primary as main
                conflicts_resolved.append({
                    'field': field,
                    'primary_value': primary_value,
                    'secondary_value': secondary_value,
                    'resolution': 'both_kept',
                    'reason': 'multiple_emails_allowed'
                })
            elif primary_value and secondary_value and primary_value != secondary_value:
                conflicts_resolved.append({
                    'field': field,
                    'primary_value': primary_value,
                    'secondary_value': secondary_value,
                    'resolution': 'primary_selected',
                    'reason': 'primary_entity_precedence'
                })
                merged_fields[field] = primary_value
            elif primary_value:
                merged_fields[field] = primary_value
            elif secondary_value:
                merged_fields[field] = secondary_value

        merged_id = self._generate_merged_id([contact1.get('id', ''), contact2.get('id', '')])
        merged_fields['id'] = merged_id
        merged_fields['merged_from'] = [contact1.get('id', ''), contact2.get('id', '')]
        merged_fields['merge_timestamp'] = datetime.utcnow().isoformat()

        return MergeResult(
            merged_entity_id=merged_id,
            source_entity_ids=[contact1.get('id', ''), contact2.get('id', '')],
            merged_fields=merged_fields,
            conflicts_resolved=conflicts_resolved,
            audit_trail={
                'merge_confidence': duplicate_match.confidence_score,
                'matching_fields': duplicate_match.matching_fields,
                'merge_timestamp': datetime.utcnow().isoformat(),
                'merge_type': 'automatic' if duplicate_match.confidence_level == ConfidenceLevel.HIGH else 'manual'
            },
            success=True
        )

    def _merge_accounts(
        self,
        duplicate_match: DuplicateMatch,
        account1: Dict[str, Any],
        account2: Dict[str, Any]
    ) -> MergeResult:
        """Merge two account entities."""
        merged_fields = {}
        conflicts_resolved = []

        primary_account, secondary_account = self._select_primary_entity(account1, account2)

        # Merge fields with account-specific logic
        for field in ['code', 'name', 'type', 'description', 'tax_type']:
            primary_value = primary_account.get(field)
            secondary_value = secondary_account.get(field)

            if field == 'code' and primary_value and secondary_value and primary_value != secondary_value:
                # For account codes, prefer the more structured one
                if len(primary_value) > len(secondary_value):
                    merged_fields[field] = primary_value
                    conflicts_resolved.append({
                        'field': field,
                        'primary_value': primary_value,
                        'secondary_value': secondary_value,
                        'resolution': 'longer_code_selected',
                        'reason': 'more_structured_code_preferred'
                    })
                else:
                    merged_fields[field] = secondary_value
                    conflicts_resolved.append({
                        'field': field,
                        'primary_value': primary_value,
                        'secondary_value': secondary_value,
                        'resolution': 'secondary_selected',
                        'reason': 'more_structured_code_preferred'
                    })
            elif primary_value and secondary_value and primary_value != secondary_value:
                conflicts_resolved.append({
                    'field': field,
                    'primary_value': primary_value,
                    'secondary_value': secondary_value,
                    'resolution': 'primary_selected',
                    'reason': 'primary_entity_precedence'
                })
                merged_fields[field] = primary_value
            elif primary_value:
                merged_fields[field] = primary_value
            elif secondary_value:
                merged_fields[field] = secondary_value

        merged_id = self._generate_merged_id([account1.get('id', ''), account2.get('id', '')])
        merged_fields['id'] = merged_id
        merged_fields['merged_from'] = [account1.get('id', ''), account2.get('id', '')]
        merged_fields['merge_timestamp'] = datetime.utcnow().isoformat()

        return MergeResult(
            merged_entity_id=merged_id,
            source_entity_ids=[account1.get('id', ''), account2.get('id', '')],
            merged_fields=merged_fields,
            conflicts_resolved=conflicts_resolved,
            audit_trail={
                'merge_confidence': duplicate_match.confidence_score,
                'matching_fields': duplicate_match.matching_fields,
                'merge_timestamp': datetime.utcnow().isoformat(),
                'merge_type': 'automatic' if duplicate_match.confidence_level == ConfidenceLevel.HIGH else 'manual'
            },
            success=True
        )

    def _select_primary_entity(
        self,
        entity1: Dict[str, Any],
        entity2: Dict[str, Any]
    ) -> Tuple[Dict[str, Any], Dict[str, Any]]:
        """Select primary entity based on completeness and recency."""
        # Calculate completeness scores
        score1 = sum(1 for v in entity1.values() if v is not None and v != "")
        score2 = sum(1 for v in entity2.values() if v is not None and v != "")

        # Check for last modified timestamps
        last_modified1 = entity1.get('last_modified', entity1.get('updated_at', ''))
        last_modified2 = entity2.get('last_modified', entity2.get('updated_at', ''))

        # Primary selection logic
        if last_modified1 and last_modified2:
            try:
                date1 = datetime.fromisoformat(last_modified1.replace('Z', '+00:00'))
                date2 = datetime.fromisoformat(last_modified2.replace('Z', '+00:00'))
                if date1 > date2:
                    return entity1, entity2
                elif date2 > date1:
                    return entity2, entity1
            except (ValueError, TypeError):
                pass

        # Fall back to completeness
        if score1 > score2:
            return entity1, entity2
        elif score2 > score1:
            return entity2, entity1
        else:
            # If equal, prefer entity1 (arbitrary but consistent)
            return entity1, entity2

    def _generate_merged_id(self, source_ids: List[str]) -> str:
        """Generate a unique ID for merged entity."""
        # Create a hash of the source IDs for consistency
        source_str = '|'.join(sorted(source_ids))
        hash_obj = hashlib.md5(source_str.encode())
        return f"merged_{hash_obj.hexdigest()[:12]}"
