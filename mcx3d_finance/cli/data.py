import click
from typing import Optional
import logging
from mcx3d_finance.tasks.sync_tasks import sync_xero_data
from mcx3d_finance.core.config import get_xero_config

logger = logging.getLogger(__name__)


@click.group()
def sync():
    """Synchronizes data from external sources."""
    pass


@sync.command("xero")
@click.option("--org-id", required=True, help="The Xero organization ID to sync.")
@click.option(
    "--incremental",
    is_flag=True,
    help="Run an incremental sync instead of a full sync.",
)
@click.option(
    "--async-mode",
    is_flag=True,
    help="Run sync asynchronously using Celery.",
)
def sync_xero(org_id: str, incremental: bool, async_mode: bool):
    """Sync data from Xero accounting system."""
    try:
        # Validate Xero configuration
        xero_config = get_xero_config()
        if not xero_config.get("client_id") or not xero_config.get("client_secret"):
            click.echo("Error: Xero API credentials not configured", err=True)
            return

        click.echo(f"Starting Xero sync for organization: {org_id}")
        click.echo(f"Mode: {'Incremental' if incremental else 'Full'}")

        if async_mode:
            # Dispatch Celery task
            task = sync_xero_data.delay(org_id, incremental)
            click.echo(f"Sync task queued with ID: {task.id}")
            click.echo("Use 'mcx3d-finance status <task_id>' to check progress")
        else:
            # Run synchronously
            result = sync_xero_data(org_id, incremental)
            if result.get("success"):
                click.echo(
                    f"Sync completed successfully. Processed {result.get('records_processed', 0)} records."
                )
            else:
                click.echo(f"Sync failed: {result.get('error')}", err=True)

    except Exception as e:
        logger.error(f"Error in Xero sync command: {e}")
        click.echo(f"Error: {e}", err=True)


@sync.command("status")
@click.argument("task_id")
def check_sync_status(task_id: str):
    """Check the status of a sync task."""
    try:
        from mcx3d_finance.tasks.celery_app import celery_app

        task = celery_app.AsyncResult(task_id)

        click.echo(f"Task ID: {task_id}")
        click.echo(f"Status: {task.status}")

        if task.status == "SUCCESS":
            result = task.result
            click.echo(f"Result: {result}")
        elif task.status == "FAILURE":
            click.echo(f"Error: {task.info}")
        elif task.status == "PENDING":
            click.echo("Task is still processing...")

    except Exception as e:
        logger.error(f"Error checking task status: {e}")
        click.echo(f"Error: {e}", err=True)


@click.command()
@click.option("--file", required=True, help="Excel file to import")
@click.option("--mapping", help="JSON file with account mapping configuration")
@click.option("--org-id", help="Target organization ID")
def import_excel(file: str, mapping: Optional[str], org_id: Optional[str]):
    """Import financial data from Excel file."""
    try:
        click.echo(f"Importing data from: {file}")

        # Implementation would go here
        from mcx3d_finance.core.data_processors import XeroDataProcessor

        # Process Excel file and import to database

        click.echo("Import completed successfully")

    except Exception as e:
        logger.error(f"Error importing Excel file: {e}")
        click.echo(f"Error: {e}", err=True)


sync.add_command(import_excel)
