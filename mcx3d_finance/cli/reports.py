import click
from mcx3d_finance.reporting.generator import ReportGenerator
from mcx3d_finance.core.financials import (
    income_statement as income_statement_calc,
    balance_sheet as balance_sheet_calc,
    cash_flow as cash_flow_calc,
)

from datetime import datetime, timedelta


def parse_period(period: str) -> (str, str):
    # TODO: Add support for monthly, and yearly periods
    if "Q" in period:
        year, quarter = period.split("-Q")
        quarter = int(quarter)
        start_month = (quarter - 1) * 3 + 1
        end_month = start_month + 2
        start_date = datetime(int(year), start_month, 1)
        end_date = datetime(int(year), end_month, 1) + timedelta(days=31)
        return start_date.strftime("%Y-%m-%d"), end_date.strftime("%Y-%m-%d")
    return None, None


def parse_date(date: str) -> (str, str):
    # TODO: Add support for monthly, and yearly periods
    if date:
        return f"{date} 00:00:00", f"{date} 23:59:59"
    return None, None


from mcx3d_finance.db.session import get_db
from mcx3d_finance.db.models import Transaction, Account
from sqlalchemy.orm import Session


def get_transactions(db: Session, organization_id: int, start_date: str, end_date: str):
    return (
        db.query(Transaction)
        .join(Account)
        .filter(
            Transaction.organization_id == organization_id,
            Transaction.date.between(start_date, end_date),
        )
        .all()
    )


@click.group()
def generate():
    """Generates financial reports."""
    pass


@generate.command("income-statement")
@click.option("--organization-id", required=True, help="The ID of the organization.")
@click.option("--period", required=True, help='The financial period (e.g., "2023-Q4").')
@click.option(
    "--format",
    type=click.Choice(["pdf", "excel", "html"]),
    default="pdf",
    help="Output format.",
)
def income_statement(organization_id, period, format):
    """Generates an income statement."""
    db = next(get_db())
    start_date, end_date = parse_period(period)
    transactions = get_transactions(db, organization_id, start_date, end_date)
    data = [
        {
            "account_type": transaction.account.type,
            "amount": transaction.amount,
        }
        for transaction in transactions
    ]
    report_data = income_statement_calc.calculate_income_statement(data)
    report_generator = ReportGenerator(report_data, "income_statement", format)
    report_buffer = report_generator.generate()

    filename = f"income_statement_{organization_id}_{period}.{format}"
    with open(filename, "wb") as f:
        f.write(report_buffer.read())
    click.echo(f"Generated {filename}")


@generate.command("balance-sheet")
@click.option("--organization-id", required=True, help="The ID of the organization.")
@click.option(
    "--date", required=True, help='The date for the balance sheet (e.g., "2023-12-31").'
)
@click.option(
    "--format",
    type=click.Choice(["pdf", "excel", "html"]),
    default="pdf",
    help="Output format.",
)
def balance_sheet(organization_id, date, format):
    """Generates a balance sheet."""
    db = next(get_db())
    start_date, end_date = parse_date(date)
    transactions = get_transactions(db, organization_id, start_date, end_date)
    data = [
        {
            "account_type": transaction.account.type,
            "amount": transaction.amount,
        }
        for transaction in transactions
    ]
    report_data = balance_sheet_calc.calculate_balance_sheet(data)
    report_generator = ReportGenerator(report_data, "balance_sheet", format)
    report_buffer = report_generator.generate()

    filename = f"balance_sheet_{organization_id}_{date}.{format}"
    with open(filename, "wb") as f:
        f.write(report_buffer.read())
    click.echo(f"Generated {filename}")


@generate.command("cash-flow")
@click.option("--organization-id", required=True, help="The ID of the organization.")
@click.option("--period", required=True, help='The financial period (e.g., "2023-Q4").')
@click.option(
    "--format",
    type=click.Choice(["pdf", "excel", "html"]),
    default="pdf",
    help="Output format.",
)
def cash_flow(organization_id, period, format):
    """Generates a cash flow statement."""
    db = next(get_db())
    start_date, end_date = parse_period(period)
    transactions = get_transactions(db, organization_id, start_date, end_date)
    data = [
        {
            "account_type": transaction.account.type,
            "amount": transaction.amount,
        }
        for transaction in transactions
    ]
    report_data = cash_flow_calc.calculate_cash_flow(data)
    report_generator = ReportGenerator(report_data, "cash_flow", format)
    report_buffer = report_generator.generate()

    filename = f"cash_flow_{organization_id}_{period}.{format}"
    with open(filename, "wb") as f:
        f.write(report_buffer.read())
    click.echo(f"Generated {filename}")
