import click
import json
from mcx3d_finance.core.valuation import dcf, multiples


@click.group()
def valuate():
    """Runs valuation models."""
    pass


@valuate.command("dcf")
@click.option(
    "--config",
    "config_path",
    required=True,
    type=click.Path(exists=True),
    help="Path to the DCF assumptions JSON file.",
)
def dcf_command(config_path):
    """Runs a Discounted Cash Flow (DCF) valuation."""
    with open(config_path) as f:
        config = json.load(f)
    valuation = dcf.calculate_dcf_valuation(
        config["financial_projections"],
        config["discount_rate"],
        config["terminal_growth_rate"],
    )
    click.echo(f"DCF Valuation: {valuation}")


@valuate.command("multiples")
@click.option(
    "--config",
    "config_path",
    required=True,
    type=click.Path(exists=True),
    help="Path to the multiples assumptions JSON file.",
)
def multiples_command(config_path):
    """Runs a valuation based on market multiples."""
    with open(config_path) as f:
        config = json.load(f)
    valuation = multiples.calculate_multiples_valuation(
        config["financial_metrics"], config["comparable_multiples"]
    )
    click.echo(f"Multiples-Based Valuation: {valuation}")
