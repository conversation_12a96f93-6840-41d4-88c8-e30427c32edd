import os
import redis

# Create a connection pool
pool = None


def get_redis_client():
    """
    Creates and returns a Redis client instance configured with environment variables.
    Uses a connection pool for efficient connection management.
    """
    global pool
    if pool is None:
        redis_host = os.getenv("REDIS_HOST", "localhost")
        redis_port = int(os.getenv("REDIS_PORT", 6379))
        pool = redis.ConnectionPool(host=redis_host, port=redis_port, db=0)

    return redis.Redis(connection_pool=pool)
