from fastapi import FastAPI
from mcx3d_finance.api import auth_routes, webhook_routes, reports, metrics

app = FastAPI(
    title="MCX3D Financials API",
    description="API for financial reporting and KPI calculation.",
    version="1.0.0",
)

app.include_router(auth_routes.router, prefix="/auth", tags=["Auth"])
app.include_router(webhook_routes.router, prefix="/webhooks", tags=["Webhooks"])
app.include_router(reports.router, prefix="/api", tags=["Reports"])
app.include_router(metrics.router, prefix="/api", tags=["Metrics"])


@app.get("/")
def read_root():
    return {"message": "Welcome to the MCX3D Financials API"}
