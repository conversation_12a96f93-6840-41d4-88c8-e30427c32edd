from sqlalchemy import Column, Integer, String, ForeignKey, DateTime, Float
from sqlalchemy.orm import relationship
from mcx3d_finance.db.session import Base


class Organization(Base):
    __tablename__ = "organizations"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String, index=True)
    xero_tenant_id = Column(String, unique=True, index=True)

    accounts = relationship("Account", back_populates="organization")
    contacts = relationship("Contact", back_populates="organization")
    transactions = relationship("Transaction", back_populates="organization")


class Account(Base):
    __tablename__ = "accounts"

    id = Column(String, primary_key=True, index=True)
    name = Column(String, index=True)
    type = Column(String)
    status = Column(String)
    organization_id = Column(Integer, ForeignKey("organizations.id"))

    organization = relationship("Organization", back_populates="accounts")


class Contact(Base):
    __tablename__ = "contacts"

    id = Column(String, primary_key=True, index=True)
    name = Column(String, index=True)
    email = Column(String)
    is_supplier = Column(String)
    is_customer = Column(String)
    organization_id = Column(Integer, ForeignKey("organizations.id"))

    organization = relationship("Organization", back_populates="contacts")


class Transaction(Base):
    __tablename__ = "transactions"

    id = Column(String, primary_key=True, index=True)
    type = Column(String)
    date = Column(DateTime)
    amount = Column(Float)
    contact_id = Column(String, ForeignKey("contacts.id"))
    account_id = Column(String, ForeignKey("accounts.id"))
    organization_id = Column(Integer, ForeignKey("organizations.id"))

    organization = relationship("Organization", back_populates="transactions")
