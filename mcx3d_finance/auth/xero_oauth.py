import logging
import secrets
import json
from typing import Optional, Dict, Any
from urllib.parse import urlparse, parse_qs
from datetime import datetime, timedelta

from xero_python.api_client import ApiClient, Configuration
from xero_python.identity import IdentityApi

from ..core.config import get_xero_config
from ..db.session import SessionLocal
from ..db.models import Organization
from ..utils.redis_client import get_redis_client

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class XeroAuthManager:
    """Complete Xero OAuth 2.0 authentication manager with token management."""

    def __init__(self):
        self.config = get_xero_config()
        self.redis_client = get_redis_client()
        self.api_client = self._create_api_client()

    def _create_api_client(self) -> ApiClient:
        """Create Xero API client with OAuth2 configuration."""
        config = Configuration()
        config.debug = self.config.get("debug", False)

        # Create API client
        api_client = ApiClient(config, pool_threads=1)

        # Set OAuth2 credentials
        api_client.set_default_header("Authorization", "Bearer")
        api_client.configuration.oauth2_client_id = self.config["client_id"]
        api_client.configuration.oauth2_client_secret = self.config["client_secret"]

        return api_client

    def generate_auth_url(self, state: Optional[str] = None) -> Optional[str]:
        """
        Generate Xero authorization URL with PKCE support.

        Args:
            state: Optional state parameter for CSRF protection

        Returns:
            Authorization URL or None if error
        """
        try:
            # Initialize OAuth2 configuration
            self.api_client.oauth2.well_known_open_id_configuration()

            # Generate state for CSRF protection if not provided
            if not state:
                state = secrets.token_urlsafe(32)

            # Store state in Redis with 10-minute expiration
            self.redis_client.set(f"xero_oauth_state:{state}", "valid", ex=600)

            # Generate authorization URL
            auth_url = self.api_client.oauth2.authorization_url(
                redirect_uri=self.config["redirect_uri"],
                scope=self.config["scopes"],
                state=state,
            )

            logger.info(f"Generated Xero auth URL with state: {state}")
            return auth_url

        except Exception as e:
            logger.error(f"Error generating Xero auth URL: {e}")
            return None

    def handle_callback(self, authorization_response_url: str) -> Dict[str, Any]:
        """
        Handle OAuth callback and exchange authorization code for tokens.

        Args:
            authorization_response_url: Full callback URL with authorization code

        Returns:
            Dict with success status and organization info or error details
        """
        db = SessionLocal()
        try:
            # Parse callback URL
            parsed_url = urlparse(authorization_response_url)
            query_params = parse_qs(parsed_url.query)

            # Validate state parameter (CSRF protection)
            state = query_params.get("state", [None])[0]
            if not state or not self.redis_client.get(f"xero_oauth_state:{state}"):
                logger.error("Invalid or missing CSRF state token")
                return {"success": False, "error": "Invalid state parameter"}

            # Clean up state from Redis
            self.redis_client.delete(f"xero_oauth_state:{state}")

            # Check for authorization errors
            error = query_params.get("error", [None])[0]
            if error:
                error_description = query_params.get(
                    "error_description", ["Unknown error"]
                )[0]
                logger.error(
                    f"OAuth authorization error: {error} - {error_description}"
                )
                return {
                    "success": False,
                    "error": f"Authorization failed: {error_description}",
                }

            # Exchange authorization code for tokens
            token = self.api_client.oauth2.fetch_token(
                authorization_response=authorization_response_url,
                redirect_uri=self.config["redirect_uri"],
            )

            # Get tenant information
            tenant_info = self._get_tenant_info(token)
            if not tenant_info:
                return {
                    "success": False,
                    "error": "Failed to retrieve tenant information",
                }

            # Store or update organization
            organization = self._store_organization_token(token, tenant_info, db)

            logger.info(f"Successfully authenticated organization: {organization.name}")
            return {
                "success": True,
                "organization_id": organization.id,
                "organization_name": organization.name,
                "tenant_id": organization.xero_tenant_id,
            }

        except Exception as oauth_error:
            logger.error(f"OAuth token exchange error: {oauth_error}")
            return {"success": False, "error": f"Token exchange failed: {str(oauth_error)}"}
        except Exception as e:
            logger.error(f"Unexpected error in OAuth callback: {e}")
            return {"success": False, "error": f"Authentication failed: {str(e)}"}
        finally:
            db.close()

    def _get_tenant_info(self, token: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        Retrieve tenant information using the access token.

        Args:
            token: OAuth2 token dictionary

        Returns:
            Tenant information or None if error
        """
        try:
            # Set token on API client
            self.api_client.set_oauth2_token(token)

            # Get identity API
            identity_api = IdentityApi(self.api_client)

            # Get connections (tenants)
            connections = identity_api.get_connections()

            if not connections or len(connections) == 0:
                logger.error("No Xero connections found")
                return None

            # Use first connection (most common case)
            connection = connections[0]

            return {
                "tenant_id": connection.tenant_id,
                "tenant_name": connection.tenant_name,
                "tenant_type": connection.tenant_type,
                "creation_date": connection.creation_date_utc,
                "auth_event_id": connection.auth_event_id,
            }

        except Exception as e:
            logger.error(f"Error retrieving tenant info: {e}")
            return None

    def _store_organization_token(
        self, token: Dict[str, Any], tenant_info: Dict[str, Any], db
    ) -> Organization:
        """
        Store or update organization with OAuth token.

        Args:
            token: OAuth2 token dictionary
            tenant_info: Xero tenant information
            db: Database session

        Returns:
            Organization object
        """
        try:
            # Check if organization already exists
            organization = (
                db.query(Organization)
                .filter(Organization.xero_tenant_id == tenant_info["tenant_id"])
                .first()
            )

            if not organization:
                # Create new organization
                organization = Organization(
                    name=tenant_info["tenant_name"],
                    xero_tenant_id=tenant_info["tenant_id"],
                    xero_tenant_type=tenant_info["tenant_type"],
                    created_at=datetime.utcnow(),
                )
                db.add(organization)
            else:
                # Update existing organization
                organization.name = tenant_info["tenant_name"]
                organization.updated_at = datetime.utcnow()

            # Store encrypted token
            organization.xero_token = self._encrypt_token(token)
            organization.token_expires_at = datetime.utcnow() + timedelta(
                seconds=token.get("expires_in", 1800)
            )

            db.commit()
            return organization

        except Exception as e:
            logger.error(f"Error storing organization token: {e}")
            db.rollback()
            raise

    def get_valid_token(self, organization_id: int) -> Optional[Dict[str, Any]]:
        """
        Get valid access token for organization, refreshing if necessary.

        Args:
            organization_id: Organization ID

        Returns:
            Valid token dictionary or None if unavailable
        """
        db = SessionLocal()
        try:
            organization = db.query(Organization).get(organization_id)
            if not organization or not organization.xero_token:
                logger.error(f"No token found for organization {organization_id}")
                return None

            token = self._decrypt_token(organization.xero_token)

            # Check if token needs refresh
            if self._token_needs_refresh(token, organization.token_expires_at):
                logger.info(f"Refreshing token for organization {organization_id}")
                token = self._refresh_token(token, organization, db)

            return token

        except Exception as e:
            logger.error(f"Error getting valid token: {e}")
            return None
        finally:
            db.close()

    def _token_needs_refresh(
        self, token: Dict[str, Any], expires_at: Optional[datetime]
    ) -> bool:
        """Check if token needs to be refreshed."""
        if not expires_at:
            return True

        # Refresh if token expires within 5 minutes
        return datetime.utcnow() + timedelta(minutes=5) >= expires_at

    def _refresh_token(
        self, token: Dict[str, Any], organization: Organization, db
    ) -> Dict[str, Any]:
        """
        Refresh OAuth2 token.

        Args:
            token: Current token dictionary
            organization: Organization object
            db: Database session

        Returns:
            Refreshed token dictionary
        """
        try:
            # Set current token
            self.api_client.set_oauth2_token(token)

            # Refresh token
            new_token = self.api_client.refresh_oauth2_token()

            # Update organization with new token
            organization.xero_token = self._encrypt_token(new_token)
            organization.token_expires_at = datetime.utcnow() + timedelta(
                seconds=new_token.get("expires_in", 1800)
            )
            organization.updated_at = datetime.utcnow()

            db.commit()

            logger.info(f"Token refreshed for organization {organization.id}")
            return new_token

        except Exception as e:
            logger.error(f"Error refreshing token: {e}")
            raise

    def _encrypt_token(self, token: Dict[str, Any]) -> str:
        """
        Encrypt token for secure storage.

        Args:
            token: Token dictionary

        Returns:
            Encrypted token string
        """
        # For production, implement proper encryption
        # For now, using base64 encoding as placeholder
        import base64

        token_json = json.dumps(token)
        return base64.b64encode(token_json.encode()).decode()

    def _decrypt_token(self, encrypted_token: str) -> Dict[str, Any]:
        """
        Decrypt stored token.

        Args:
            encrypted_token: Encrypted token string

        Returns:
            Decrypted token dictionary
        """
        # For production, implement proper decryption
        # For now, using base64 decoding as placeholder
        import base64

        token_json = base64.b64decode(encrypted_token.encode()).decode()
        return json.loads(token_json)

    def revoke_token(self, organization_id: int) -> bool:
        """
        Revoke OAuth token for organization.

        Args:
            organization_id: Organization ID

        Returns:
            True if successful, False otherwise
        """
        db = SessionLocal()
        try:
            organization = db.query(Organization).get(organization_id)
            if not organization or not organization.xero_token:
                return False

            token = self._decrypt_token(organization.xero_token)

            # Set token and revoke
            self.api_client.set_oauth2_token(token)
            # Note: Xero doesn't have a direct revoke endpoint,
            # so we'll just clear the stored token

            # Clear token from database
            organization.xero_token = None
            organization.token_expires_at = None
            organization.updated_at = datetime.utcnow()

            db.commit()

            logger.info(f"Token revoked for organization {organization_id}")
            return True

        except Exception as e:
            logger.error(f"Error revoking token: {e}")
            return False
        finally:
            db.close()


# Convenience functions for backward compatibility
def get_xero_api_client():
    """Get configured Xero API client."""
    auth_manager = XeroAuthManager()
    return auth_manager.api_client


def generate_auth_url():
    """Generate Xero authorization URL."""
    auth_manager = XeroAuthManager()
    return auth_manager.generate_auth_url()


def handle_callback(authorization_response_url: str):
    """Handle OAuth callback."""
    auth_manager = XeroAuthManager()
    result = auth_manager.handle_callback(authorization_response_url)

    if result["success"]:
        return "/success"
    else:
        return "/error"
