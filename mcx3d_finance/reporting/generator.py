"""
Enhanced report generator with PDF and Excel output capabilities.
"""

import logging
from typing import Dict, Any, List

# PDF generation
from reportlab.lib import colors
from reportlab.lib.pagesizes import letter
from reportlab.platypus import (
    SimpleDocTemplate,
    Table,
    TableStyle,
    Paragraph,
    Spacer,
    PageBreak,
)
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch
from reportlab.lib.enums import TA_CENTER, TA_RIGHT

# Excel generation
from openpyxl import Workbook
from openpyxl.styles import Font, Alignment

logger = logging.getLogger(__name__)


class ReportGenerator:
    """Enhanced report generator with multiple output formats."""

    def __init__(self):
        self.styles = getSampleStyleSheet()
        self._setup_custom_styles()

    def _setup_custom_styles(self):
        """Setup custom styles for PDF generation."""
        # Company header style
        self.styles.add(
            ParagraphStyle(
                name="CompanyHeader",
                parent=self.styles["Heading1"],
                fontSize=16,
                spaceAfter=6,
                alignment=TA_CENTER,
                textColor=colors.black,
            )
        )

        # Statement title style
        self.styles.add(
            ParagraphStyle(
                name="StatementTitle",
                parent=self.styles["Heading2"],
                fontSize=14,
                spaceAfter=12,
                alignment=TA_CENTER,
                textColor=colors.black,
            )
        )

        # Section header style
        self.styles.add(
            ParagraphStyle(
                name="SectionHeader",
                parent=self.styles["Heading3"],
                fontSize=12,
                spaceBefore=12,
                spaceAfter=6,
                textColor=colors.black,
            )
        )

        # Financial data style
        self.styles.add(
            ParagraphStyle(
                name="FinancialData",
                parent=self.styles["Normal"],
                fontSize=10,
                alignment=TA_RIGHT,
            )
        )

    def generate_balance_sheet_pdf(
        self, balance_sheet_data: Dict[str, Any], output_path: str
    ):
        """Generate NASDAQ-compliant balance sheet PDF."""
        try:
            logger.info(f"Generating balance sheet PDF: {output_path}")

            doc = SimpleDocTemplate(
                output_path,
                pagesize=letter,
                rightMargin=72,
                leftMargin=72,
                topMargin=72,
                bottomMargin=18,
            )

            story = []

            # Header information
            header = balance_sheet_data.get("header", {})
            story.append(
                Paragraph(
                    header.get("company_name", "Company Name"),
                    self.styles["CompanyHeader"],
                )
            )
            story.append(
                Paragraph(
                    header.get("statement_title", "BALANCE SHEET"),
                    self.styles["StatementTitle"],
                )
            )
            story.append(
                Paragraph(
                    f"As of {header.get('reporting_date', 'Date')}",
                    self.styles["Normal"],
                )
            )

            if header.get("comparative_date"):
                story.append(
                    Paragraph(
                        f"(With comparative figures as of {header.get('comparative_date')})",
                        self.styles["Normal"],
                    )
                )

            story.append(
                Paragraph(
                    f"(All amounts in {header.get('amounts_in', 'thousands')} of {header.get('currency', 'USD')})",
                    self.styles["Normal"],
                )
            )
            story.append(Spacer(1, 20))

            # Assets section
            story.append(Paragraph("ASSETS", self.styles["SectionHeader"]))
            assets_table_data = self._build_balance_sheet_assets_table(
                balance_sheet_data.get("assets", {})
            )
            assets_table = self._create_financial_table(assets_table_data)
            story.append(assets_table)
            story.append(Spacer(1, 20))

            # Liabilities and Equity section
            story.append(
                Paragraph(
                    "LIABILITIES AND STOCKHOLDERS' EQUITY", self.styles["SectionHeader"]
                )
            )
            liab_equity_table_data = self._build_balance_sheet_liabilities_table(
                balance_sheet_data.get("liabilities_and_equity", {})
            )
            liab_equity_table = self._create_financial_table(liab_equity_table_data)
            story.append(liab_equity_table)
            story.append(Spacer(1, 20))

            # Financial analysis
            if "financial_analysis" in balance_sheet_data:
                story.append(PageBreak())
                story.append(
                    Paragraph("FINANCIAL ANALYSIS", self.styles["SectionHeader"])
                )
                analysis_table_data = self._build_financial_analysis_table(
                    balance_sheet_data["financial_analysis"]
                )
                analysis_table = self._create_financial_table(analysis_table_data)
                story.append(analysis_table)

            # Compliance certifications
            if "compliance" in balance_sheet_data:
                story.append(Spacer(1, 20))
                story.append(
                    Paragraph("COMPLIANCE CERTIFICATIONS", self.styles["SectionHeader"])
                )
                for cert in balance_sheet_data["compliance"].get("certifications", []):
                    story.append(Paragraph(f"• {cert}", self.styles["Normal"]))
                    story.append(Spacer(1, 6))

            doc.build(story)
            logger.info("Balance sheet PDF generated successfully")

        except Exception as e:
            logger.error(f"Error generating balance sheet PDF: {e}")
            raise

    def generate_income_statement_pdf(
        self, income_statement_data: Dict[str, Any], output_path: str
    ):
        """Generate NASDAQ-compliant income statement PDF."""
        try:
            logger.info(f"Generating income statement PDF: {output_path}")

            doc = SimpleDocTemplate(
                output_path,
                pagesize=letter,
                rightMargin=72,
                leftMargin=72,
                topMargin=72,
                bottomMargin=18,
            )

            story = []

            # Header information
            header = income_statement_data.get("header", {})
            story.append(
                Paragraph(
                    header.get("company_name", "Company Name"),
                    self.styles["CompanyHeader"],
                )
            )
            story.append(
                Paragraph(
                    header.get("statement_title", "INCOME STATEMENT"),
                    self.styles["StatementTitle"],
                )
            )
            story.append(
                Paragraph(
                    f"For the {header.get('period_description', 'Period')}",
                    self.styles["Normal"],
                )
            )

            if header.get("comparative_period"):
                story.append(
                    Paragraph(
                        f"(With comparative figures for the {header.get('comparative_period')})",
                        self.styles["Normal"],
                    )
                )

            story.append(
                Paragraph(
                    f"(All amounts in {header.get('amounts_in', 'thousands')} of {header.get('currency', 'USD')}, except per share data)",
                    self.styles["Normal"],
                )
            )
            story.append(Spacer(1, 20))

            # Revenue section
            story.append(Paragraph("REVENUE", self.styles["SectionHeader"]))
            revenue_table_data = self._build_income_statement_revenue_table(
                income_statement_data.get("revenue", {})
            )
            revenue_table = self._create_financial_table(revenue_table_data)
            story.append(revenue_table)
            story.append(Spacer(1, 12))

            # Cost of revenue and gross profit
            cost_table_data = self._build_income_statement_cost_table(
                income_statement_data.get("cost_of_revenue", {}),
                income_statement_data.get("gross_profit", {}),
            )
            cost_table = self._create_financial_table(cost_table_data)
            story.append(cost_table)
            story.append(Spacer(1, 12))

            # Operating expenses and operating income
            opex_table_data = self._build_income_statement_opex_table(
                income_statement_data.get("operating_expenses", {}),
                income_statement_data.get("operating_income", {}),
            )
            opex_table = self._create_financial_table(opex_table_data)
            story.append(opex_table)
            story.append(Spacer(1, 12))

            # Non-operating and net income
            final_table_data = self._build_income_statement_final_table(
                income_statement_data.get("non_operating_income_expense", {}),
                income_statement_data.get("income_before_taxes", {}),
                income_statement_data.get("income_tax_expense", {}),
                income_statement_data.get("net_income", {}),
            )
            final_table = self._create_financial_table(final_table_data)
            story.append(final_table)
            story.append(Spacer(1, 20))

            # Earnings per share
            if "earnings_per_share" in income_statement_data:
                eps_table_data = self._build_eps_table(
                    income_statement_data["earnings_per_share"]
                )
                eps_table = self._create_financial_table(eps_table_data)
                story.append(eps_table)
                story.append(Spacer(1, 20))

            # Financial analysis
            if "financial_analysis" in income_statement_data:
                story.append(PageBreak())
                story.append(
                    Paragraph("FINANCIAL ANALYSIS", self.styles["SectionHeader"])
                )
                analysis_table_data = self._build_income_analysis_table(
                    income_statement_data["financial_analysis"]
                )
                analysis_table = self._create_financial_table(analysis_table_data)
                story.append(analysis_table)

            doc.build(story)
            logger.info("Income statement PDF generated successfully")

        except Exception as e:
            logger.error(f"Error generating income statement PDF: {e}")
            raise

    def generate_balance_sheet_excel(
        self, balance_sheet_data: Dict[str, Any], output_path: str
    ):
        """Generate NASDAQ-compliant balance sheet Excel file."""
        try:
            logger.info(f"Generating balance sheet Excel: {output_path}")

            wb = Workbook()
            ws = wb.active
            ws.title = "Balance Sheet"

            # Setup styles
            header_font = Font(bold=True, size=14)
            section_font = Font(bold=True, size=12)
            currency_format = "#,##0"

            row = 1

            # Header information
            header = balance_sheet_data.get("header", {})
            ws.merge_cells(f"A{row}:D{row}")
            ws[f"A{row}"] = header.get("company_name", "Company Name")
            ws[f"A{row}"].font = header_font
            ws[f"A{row}"].alignment = Alignment(horizontal="center")
            row += 1

            ws.merge_cells(f"A{row}:D{row}")
            ws[f"A{row}"] = header.get("statement_title", "BALANCE SHEET")
            ws[f"A{row}"].font = section_font
            ws[f"A{row}"].alignment = Alignment(horizontal="center")
            row += 1

            ws.merge_cells(f"A{row}:D{row}")
            ws[f"A{row}"] = f"As of {header.get('reporting_date', 'Date')}"
            ws[f"A{row}"].alignment = Alignment(horizontal="center")
            row += 1

            if header.get("comparative_date"):
                ws.merge_cells(f"A{row}:D{row}")
                ws[f"A{row}"] = (
                    f"(With comparative figures as of {header.get('comparative_date')})"
                )
                ws[f"A{row}"].alignment = Alignment(horizontal="center")
                row += 1

            ws.merge_cells(f"A{row}:D{row}")
            ws[f"A{row}"] = (
                f"(All amounts in {header.get('amounts_in', 'thousands')} of {header.get('currency', 'USD')})"
            )
            ws[f"A{row}"].alignment = Alignment(horizontal="center")
            row += 2

            # Column headers
            ws["A" + str(row)] = "Account"
            ws["B" + str(row)] = header.get("reporting_date", "Current")
            if header.get("comparative_date"):
                ws["C" + str(row)] = header.get("comparative_date", "Comparative")

            for col in ["A", "B", "C"]:
                ws[col + str(row)].font = section_font
            row += 1

            # Assets section
            row = self._add_excel_balance_sheet_section(
                ws, row, "ASSETS", balance_sheet_data.get("assets", {}), currency_format
            )
            row += 1

            # Liabilities and Equity section
            row = self._add_excel_balance_sheet_section(
                ws,
                row,
                "LIABILITIES AND STOCKHOLDERS' EQUITY",
                balance_sheet_data.get("liabilities_and_equity", {}),
                currency_format,
            )

            # Auto-adjust column widths
            for column in ws.columns:
                max_length = 0
                column_letter = column[0].column_letter
                for cell in column:
                    try:
                        if len(str(cell.value)) > max_length:
                            max_length = len(str(cell.value))
                    except (TypeError, ValueError):
                        pass
                adjusted_width = min(max_length + 2, 50)
                ws.column_dimensions[column_letter].width = adjusted_width

            # Add financial analysis sheet
            if "financial_analysis" in balance_sheet_data:
                analysis_ws = wb.create_sheet("Financial Analysis")
                self._add_excel_financial_analysis(
                    analysis_ws, balance_sheet_data["financial_analysis"]
                )

            wb.save(output_path)
            logger.info("Balance sheet Excel generated successfully")

        except Exception as e:
            logger.error(f"Error generating balance sheet Excel: {e}")
            raise

    def generate_income_statement_excel(
        self, income_statement_data: Dict[str, Any], output_path: str
    ):
        """Generate NASDAQ-compliant income statement Excel file."""
        try:
            logger.info(f"Generating income statement Excel: {output_path}")

            wb = Workbook()
            ws = wb.active
            ws.title = "Income Statement"

            # Setup styles
            header_font = Font(bold=True, size=14)
            section_font = Font(bold=True, size=12)
            currency_format = "#,##0"

            row = 1

            # Header information
            header = income_statement_data.get("header", {})
            ws.merge_cells(f"A{row}:D{row}")
            ws[f"A{row}"] = header.get("company_name", "Company Name")
            ws[f"A{row}"].font = header_font
            ws[f"A{row}"].alignment = Alignment(horizontal="center")
            row += 1

            ws.merge_cells(f"A{row}:D{row}")
            ws[f"A{row}"] = header.get("statement_title", "INCOME STATEMENT")
            ws[f"A{row}"].font = section_font
            ws[f"A{row}"].alignment = Alignment(horizontal="center")
            row += 1

            ws.merge_cells(f"A{row}:D{row}")
            ws[f"A{row}"] = f"For the {header.get('period_description', 'Period')}"
            ws[f"A{row}"].alignment = Alignment(horizontal="center")
            row += 1

            if header.get("comparative_period"):
                ws.merge_cells(f"A{row}:D{row}")
                ws[f"A{row}"] = (
                    f"(With comparative figures for the {header.get('comparative_period')})"
                )
                ws[f"A{row}"].alignment = Alignment(horizontal="center")
                row += 1

            ws.merge_cells(f"A{row}:D{row}")
            ws[f"A{row}"] = (
                f"(All amounts in {header.get('amounts_in', 'thousands')} of {header.get('currency', 'USD')}, except per share data)"
            )
            ws[f"A{row}"].alignment = Alignment(horizontal="center")
            row += 2

            # Column headers
            ws["A" + str(row)] = "Account"
            ws["B" + str(row)] = "Current Period"
            if header.get("comparative_period"):
                ws["C" + str(row)] = "Comparative Period"

            for col in ["A", "B", "C"]:
                ws[col + str(row)].font = section_font
            row += 1

            # Add income statement sections
            sections = [
                ("REVENUE", income_statement_data.get("revenue", {})),
                ("COST OF REVENUE", income_statement_data.get("cost_of_revenue", {})),
                (
                    "GROSS PROFIT",
                    {"gross_profit": income_statement_data.get("gross_profit", {})},
                ),
                (
                    "OPERATING EXPENSES",
                    income_statement_data.get("operating_expenses", {}),
                ),
                (
                    "OPERATING INCOME",
                    {
                        "operating_income": income_statement_data.get(
                            "operating_income", {}
                        )
                    },
                ),
                (
                    "NON-OPERATING INCOME (EXPENSE)",
                    income_statement_data.get("non_operating_income_expense", {}),
                ),
                (
                    "INCOME BEFORE TAXES",
                    {
                        "income_before_taxes": income_statement_data.get(
                            "income_before_taxes", {}
                        )
                    },
                ),
                (
                    "INCOME TAX EXPENSE",
                    {
                        "income_tax_expense": income_statement_data.get(
                            "income_tax_expense", {}
                        )
                    },
                ),
                (
                    "NET INCOME",
                    {"net_income": income_statement_data.get("net_income", {})},
                ),
            ]

            for section_name, section_data in sections:
                row = self._add_excel_income_statement_section(
                    ws, row, section_name, section_data, currency_format
                )
                row += 1

            # Add EPS section
            if "earnings_per_share" in income_statement_data:
                row = self._add_excel_eps_section(
                    ws, row, income_statement_data["earnings_per_share"]
                )

            # Auto-adjust column widths
            for column in ws.columns:
                max_length = 0
                column_letter = column[0].column_letter
                for cell in column:
                    try:
                        if len(str(cell.value)) > max_length:
                            max_length = len(str(cell.value))
                    except (TypeError, ValueError):
                        pass
                adjusted_width = min(max_length + 2, 50)
                ws.column_dimensions[column_letter].width = adjusted_width

            # Add financial analysis sheet
            if "financial_analysis" in income_statement_data:
                analysis_ws = wb.create_sheet("Financial Analysis")
                self._add_excel_income_analysis(
                    analysis_ws, income_statement_data["financial_analysis"]
                )

            wb.save(output_path)
            logger.info("Income statement Excel generated successfully")

        except Exception as e:
            logger.error(f"Error generating income statement Excel: {e}")
            raise

    # Helper methods for PDF table building
    def _build_balance_sheet_assets_table(
        self, assets_data: Dict[str, Any]
    ) -> List[List[str]]:
        """Build assets table data for PDF."""
        table_data = []

        # Current assets
        current_assets = assets_data.get("current_assets", {})
        table_data.append(["CURRENT ASSETS:", "", ""])

        for key, value in current_assets.items():
            if key != "title" and key != "total_current_assets":
                label = key.replace("_", " ").title()
                current_val = self._format_currency(value.get("current", 0))
                comp_val = (
                    self._format_currency(value.get("comparative", 0))
                    if value.get("comparative") is not None
                    else ""
                )
                table_data.append([f"  {label}", current_val, comp_val])

        # Total current assets
        total_current = current_assets.get("total_current_assets", {})
        table_data.append(
            [
                "  Total current assets",
                self._format_currency(total_current.get("current", 0)),
                (
                    self._format_currency(total_current.get("comparative", 0))
                    if total_current.get("comparative") is not None
                    else ""
                ),
            ]
        )

        table_data.append(["", "", ""])  # Spacer

        # Non-current assets
        non_current_assets = assets_data.get("non_current_assets", {})
        table_data.append(["NON-CURRENT ASSETS:", "", ""])

        for key, value in non_current_assets.items():
            if key != "title" and key != "total_non_current_assets":
                label = key.replace("_", " ").title()
                current_val = self._format_currency(value.get("current", 0))
                comp_val = (
                    self._format_currency(value.get("comparative", 0))
                    if value.get("comparative") is not None
                    else ""
                )
                table_data.append([f"  {label}", current_val, comp_val])

        # Total non-current assets
        total_non_current = non_current_assets.get("total_non_current_assets", {})
        table_data.append(
            [
                "  Total non-current assets",
                self._format_currency(total_non_current.get("current", 0)),
                (
                    self._format_currency(total_non_current.get("comparative", 0))
                    if total_non_current.get("comparative") is not None
                    else ""
                ),
            ]
        )

        table_data.append(["", "", ""])  # Spacer

        # Total assets
        total_assets = assets_data.get("total_assets", {})
        table_data.append(
            [
                "TOTAL ASSETS",
                self._format_currency(total_assets.get("current", 0)),
                (
                    self._format_currency(total_assets.get("comparative", 0))
                    if total_assets.get("comparative") is not None
                    else ""
                ),
            ]
        )

        return table_data

    def _build_balance_sheet_liabilities_table(
        self, liab_equity_data: Dict[str, Any]
    ) -> List[List[str]]:
        """Build liabilities and equity table data for PDF."""
        table_data = []

        # Current liabilities
        current_liabilities = liab_equity_data.get("current_liabilities", {})
        table_data.append(["CURRENT LIABILITIES:", "", ""])

        for key, value in current_liabilities.items():
            if key != "title" and key != "total_current_liabilities":
                label = key.replace("_", " ").title()
                current_val = self._format_currency(value.get("current", 0))
                comp_val = (
                    self._format_currency(value.get("comparative", 0))
                    if value.get("comparative") is not None
                    else ""
                )
                table_data.append([f"  {label}", current_val, comp_val])

        # Total current liabilities
        total_current_liab = current_liabilities.get("total_current_liabilities", {})
        table_data.append(
            [
                "  Total current liabilities",
                self._format_currency(total_current_liab.get("current", 0)),
                (
                    self._format_currency(total_current_liab.get("comparative", 0))
                    if total_current_liab.get("comparative") is not None
                    else ""
                ),
            ]
        )

        table_data.append(["", "", ""])  # Spacer

        # Non-current liabilities
        non_current_liabilities = liab_equity_data.get("non_current_liabilities", {})
        table_data.append(["NON-CURRENT LIABILITIES:", "", ""])

        for key, value in non_current_liabilities.items():
            if key != "title" and key != "total_non_current_liabilities":
                label = key.replace("_", " ").title()
                current_val = self._format_currency(value.get("current", 0))
                comp_val = (
                    self._format_currency(value.get("comparative", 0))
                    if value.get("comparative") is not None
                    else ""
                )
                table_data.append([f"  {label}", current_val, comp_val])

        # Total non-current liabilities
        total_non_current_liab = non_current_liabilities.get(
            "total_non_current_liabilities", {}
        )
        table_data.append(
            [
                "  Total non-current liabilities",
                self._format_currency(total_non_current_liab.get("current", 0)),
                (
                    self._format_currency(total_non_current_liab.get("comparative", 0))
                    if total_non_current_liab.get("comparative") is not None
                    else ""
                ),
            ]
        )

        # Total liabilities
        total_liabilities = liab_equity_data.get("total_liabilities", {})
        table_data.append(
            [
                "  Total liabilities",
                self._format_currency(total_liabilities.get("current", 0)),
                (
                    self._format_currency(total_liabilities.get("comparative", 0))
                    if total_liabilities.get("comparative") is not None
                    else ""
                ),
            ]
        )

        table_data.append(["", "", ""])  # Spacer

        # Stockholders' equity
        stockholders_equity = liab_equity_data.get("stockholders_equity", {})
        table_data.append(["STOCKHOLDERS' EQUITY:", "", ""])

        for key, value in stockholders_equity.items():
            if key != "title" and key != "total_stockholders_equity":
                label = key.replace("_", " ").title()
                current_val = self._format_currency(value.get("current", 0))
                comp_val = (
                    self._format_currency(value.get("comparative", 0))
                    if value.get("comparative") is not None
                    else ""
                )
                table_data.append([f"  {label}", current_val, comp_val])

        # Total stockholders' equity
        total_equity = stockholders_equity.get("total_stockholders_equity", {})
        table_data.append(
            [
                "  Total stockholders' equity",
                self._format_currency(total_equity.get("current", 0)),
                (
                    self._format_currency(total_equity.get("comparative", 0))
                    if total_equity.get("comparative") is not None
                    else ""
                ),
            ]
        )

        # Total liabilities and equity
        total_liab_equity = liab_equity_data.get("total_liabilities_and_equity", {})
        table_data.append(
            [
                "TOTAL LIABILITIES AND EQUITY",
                self._format_currency(total_liab_equity.get("current", 0)),
                (
                    self._format_currency(total_liab_equity.get("comparative", 0))
                    if total_liab_equity.get("comparative") is not None
                    else ""
                ),
            ]
        )

        return table_data

    def _create_financial_table(self, table_data: List[List[str]]) -> Table:
        """Create a formatted financial table for PDF."""
        table = Table(table_data, colWidths=[4 * inch, 1.5 * inch, 1.5 * inch])

        table.setStyle(
            TableStyle(
                [
                    ("ALIGN", (0, 0), (-1, -1), "LEFT"),
                    ("ALIGN", (1, 0), (-1, -1), "RIGHT"),
                    ("FONTNAME", (0, 0), (-1, -1), "Helvetica"),
                    ("FONTSIZE", (0, 0), (-1, -1), 10),
                    ("GRID", (0, 0), (-1, -1), 0.5, colors.black),
                    ("VALIGN", (0, 0), (-1, -1), "MIDDLE"),
                ]
            )
        )

        return table

    def _format_currency(self, amount: float) -> str:
        """Format currency amount for display."""
        if amount == 0:
            return "-"
        return f"{amount:,}"

    # Additional helper methods would continue here...
    # (Excel helper methods, income statement table builders, etc.)
