"""
Enhanced Xero data synchronization with real-time updates.
"""

from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
import logging
from sqlalchemy.orm import Session
from mcx3d_finance.auth.xero_oauth import XeroAuthManager
from mcx3d_finance.db.models import Organization, Transaction, Account
from mcx3d_finance.core.data_processors import XeroDataProcessor

logger = logging.getLogger(__name__)


class XeroSyncEngine:
    """Enhanced Xero synchronization engine."""

    def __init__(self, db_session: Session):
        self.db = db_session
        self.auth_manager = XeroAuthManager()
        self.data_processor = XeroDataProcessor()

    async def sync_organization_data(
        self,
        org_id: str,
        incremental: bool = True,
        sync_types: Optional[List[str]] = None,
    ) -> Dict[str, Any]:
        """Sync all data for an organization."""

        sync_types = sync_types or [
            "accounts",
            "contacts",
            "transactions",
            "invoices",
            "bills",
            "bank_transactions",
        ]

        results = {
            "org_id": org_id,
            "sync_started": datetime.utcnow().isoformat(),
            "incremental": incremental,
            "results": {},
        }

        try:
            # Get Xero client
            xero_client = await self.auth_manager.get_client_for_org(org_id)

            # Sync each data type
            for sync_type in sync_types:
                logger.info(f"Syncing {sync_type} for org {org_id}")

                sync_result = await self._sync_data_type(
                    xero_client, org_id, sync_type, incremental
                )

                results["results"][sync_type] = sync_result

            results["sync_completed"] = datetime.utcnow().isoformat()
            results["success"] = True

            return results

        except Exception as e:
            logger.error(f"Error syncing org {org_id}: {e}")
            results["error"] = str(e)
            results["success"] = False
            return results
