import logging
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
from xero_python.api_client import Api<PERSON>lient
from xero_python.api_client.configuration import Configuration
from xero_python.api_client.oauth2 import OAuth2Token
from xero_python.accounting import AccountingApi
from xero_python.exceptions import ApiException
from xero_python.models.accounting import (
    Accounts,
    BankTransactions,
    Invoices,
    Bills,
    Contacts,
    ReportWithRows,
    TrialBalanceResponse,
    ProfitAndLossResponse,
)

from ..db.session import SessionLocal
from ..db.models import Organization
from ..core.config import get_xero_config

logger = logging.getLogger(__name__)


class XeroClient:
    """Enhanced Xero API client with comprehensive financial data access."""

    def __init__(self, organization_id: int):
        self.organization_id = organization_id
        self.organization = self._get_organization()
        if not self.organization or not self.organization.xero_token:
            raise ValueError("Organization not found or Xero token is missing.")

        self.api_client = self._create_api_client()
        self.accounting_api = AccountingApi(self.api_client)
        self.tenant_id = self.organization.xero_tenant_id

    def _get_organization(self):
        db = SessionLocal()
        try:
            return db.query(Organization).get(self.organization_id)
        finally:
            db.close()

    def _token_refreshed_cb(self, new_token: dict):
        """Callback to save refreshed token."""
        db = SessionLocal()
        try:
            self.organization.xero_token = new_token
            db.merge(self.organization)
            db.commit()
            logger.info(f"Xero token refreshed for organization {self.organization_id}")
        finally:
            db.close()

    def _create_api_client(self):
        """Creates and configures the Xero API client."""
        xero_config = get_xero_config()

        api_client = ApiClient(
            Configuration(
                debug=xero_config.get("debug", False),
                oauth2_token=OAuth2Token(
                    client_id=xero_config["client_id"],
                    client_secret=xero_config["client_secret"],
                ),
            ),
            pool_threads=1,
        )

        api_client.set_oauth2_token(self.organization.xero_token)
        return api_client

    def get_chart_of_accounts(self) -> List[Dict[str, Any]]:
        """
        Retrieve complete chart of accounts from Xero.
        Maps to GET /Accounts endpoint.
        """
        try:
            logger.info(f"Fetching chart of accounts for tenant {self.tenant_id}")

            accounts_response = self.accounting_api.get_accounts(
                xero_tenant_id=self.tenant_id, where='Status=="ACTIVE"'
            )

            accounts_data = []
            if accounts_response.accounts:
                for account in accounts_response.accounts:
                    account_data = {
                        "account_id": account.account_id,
                        "account_code": account.code,
                        "account_name": account.name,
                        "account_type": account.type.value if account.type else None,
                        "account_class": (
                            account.class_.value if account.class_ else None
                        ),
                        "status": account.status.value if account.status else None,
                        "description": account.description,
                        "tax_type": account.tax_type,
                        "enable_payments": account.enable_payments_to_account,
                        "show_in_expense_claims": account.show_in_expense_claims,
                        "bank_account_number": account.bank_account_number,
                        "bank_account_type": (
                            account.bank_account_type.value
                            if account.bank_account_type
                            else None
                        ),
                        "currency_code": (
                            account.currency_code.value
                            if account.currency_code
                            else None
                        ),
                    }
                    accounts_data.append(account_data)

            logger.info(f"Retrieved {len(accounts_data)} accounts")
            return accounts_data

        except ApiException as e:
            logger.error(f"Xero API error fetching accounts: {e}")
            raise
        except Exception as e:
            logger.error(f"Error fetching chart of accounts: {e}")
            raise

    def get_trial_balance(self, date: datetime) -> Dict[str, Any]:
        """
        Get trial balance report for a specific date.
        Maps to GET /Reports/TrialBalance endpoint.
        """
        try:
            logger.info(f"Fetching trial balance for {date.strftime('%Y-%m-%d')}")

            trial_balance = self.accounting_api.get_report_trial_balance(
                xero_tenant_id=self.tenant_id, date=date, payments_only=False
            )

            # Process trial balance data
            trial_balance_data = {
                "report_date": date.isoformat(),
                "report_name": trial_balance.report_name,
                "report_type": trial_balance.report_type,
                "accounts": [],
            }

            if trial_balance.reports and len(trial_balance.reports) > 0:
                report = trial_balance.reports[0]
                if report.rows:
                    for row in report.rows:
                        if row.cells and len(row.cells) >= 3:
                            account_data = {
                                "account_name": (
                                    row.cells[0].value if row.cells[0] else ""
                                ),
                                "account_code": (
                                    row.cells[1].value
                                    if len(row.cells) > 1 and row.cells[1]
                                    else ""
                                ),
                                "debit": (
                                    float(row.cells[2].value)
                                    if len(row.cells) > 2
                                    and row.cells[2]
                                    and row.cells[2].value
                                    else 0.0
                                ),
                                "credit": (
                                    float(row.cells[3].value)
                                    if len(row.cells) > 3
                                    and row.cells[3]
                                    and row.cells[3].value
                                    else 0.0
                                ),
                            }
                            trial_balance_data["accounts"].append(account_data)

            return trial_balance_data

        except ApiException as e:
            logger.error(f"Xero API error fetching trial balance: {e}")
            raise
        except Exception as e:
            logger.error(f"Error fetching trial balance: {e}")
            raise

    def get_profit_and_loss(
        self, from_date: datetime, to_date: datetime
    ) -> Dict[str, Any]:
        """
        Get Profit & Loss report for a date range.
        Maps to GET /Reports/ProfitAndLoss endpoint.
        """
        try:
            logger.info(
                f"Fetching P&L from {from_date.strftime('%Y-%m-%d')} to {to_date.strftime('%Y-%m-%d')}"
            )

            pl_report = self.accounting_api.get_report_profit_and_loss(
                xero_tenant_id=self.tenant_id,
                from_date=from_date,
                to_date=to_date,
                periods=1,
                time_frame="MONTH",
            )

            # Process P&L data
            pl_data = {
                "from_date": from_date.isoformat(),
                "to_date": to_date.isoformat(),
                "report_name": pl_report.report_name,
                "sections": [],
            }

            if pl_report.reports and len(pl_report.reports) > 0:
                report = pl_report.reports[0]
                if report.rows:
                    current_section = None

                    for row in report.rows:
                        if row.row_type == "Header":
                            current_section = {
                                "section_name": (
                                    row.cells[0].value
                                    if row.cells and row.cells[0]
                                    else ""
                                ),
                                "accounts": [],
                            }
                            pl_data["sections"].append(current_section)
                        elif row.row_type == "Row" and current_section and row.cells:
                            account_data = {
                                "account_name": (
                                    row.cells[0].value if row.cells[0] else ""
                                ),
                                "amount": (
                                    float(row.cells[1].value)
                                    if len(row.cells) > 1
                                    and row.cells[1]
                                    and row.cells[1].value
                                    else 0.0
                                ),
                            }
                            current_section["accounts"].append(account_data)

            return pl_data

        except ApiException as e:
            logger.error(f"Xero API error fetching P&L: {e}")
            raise
        except Exception as e:
            logger.error(f"Error fetching profit and loss: {e}")
            raise

    def get_bank_transactions(
        self, from_date: Optional[datetime] = None, to_date: Optional[datetime] = None
    ) -> List[Dict[str, Any]]:
        """
        Get bank transactions from Xero.
        Maps to GET /BankTransactions endpoint.
        """
        try:
            # Default to last 90 days if no dates provided
            if not to_date:
                to_date = datetime.now()
            if not from_date:
                from_date = to_date - timedelta(days=90)

            logger.info(
                f"Fetching bank transactions from {from_date.strftime('%Y-%m-%d')} to {to_date.strftime('%Y-%m-%d')}"
            )

            where_clause = f"Date >= DateTime({from_date.year}, {from_date.month}, {from_date.day}) AND Date <= DateTime({to_date.year}, {to_date.month}, {to_date.day})"

            bank_transactions = self.accounting_api.get_bank_transactions(
                xero_tenant_id=self.tenant_id, where=where_clause, order="Date DESC"
            )

            transactions_data = []
            if bank_transactions.bank_transactions:
                for transaction in bank_transactions.bank_transactions:
                    transaction_data = {
                        "transaction_id": transaction.bank_transaction_id,
                        "type": transaction.type.value if transaction.type else None,
                        "date": transaction.date,
                        "reference": transaction.reference,
                        "amount": (
                            float(transaction.total) if transaction.total else 0.0
                        ),
                        "currency_code": (
                            transaction.currency_code.value
                            if transaction.currency_code
                            else "USD"
                        ),
                        "bank_account": {
                            "account_id": (
                                transaction.bank_account.account_id
                                if transaction.bank_account
                                else None
                            ),
                            "account_code": (
                                transaction.bank_account.code
                                if transaction.bank_account
                                else None
                            ),
                            "account_name": (
                                transaction.bank_account.name
                                if transaction.bank_account
                                else None
                            ),
                        },
                        "contact": {
                            "contact_id": (
                                transaction.contact.contact_id
                                if transaction.contact
                                else None
                            ),
                            "name": (
                                transaction.contact.name
                                if transaction.contact
                                else None
                            ),
                        },
                        "line_items": [],
                    }

                    # Process line items
                    if transaction.line_items:
                        for line_item in transaction.line_items:
                            line_data = {
                                "description": line_item.description,
                                "quantity": (
                                    float(line_item.quantity)
                                    if line_item.quantity
                                    else 1.0
                                ),
                                "unit_amount": (
                                    float(line_item.unit_amount)
                                    if line_item.unit_amount
                                    else 0.0
                                ),
                                "line_amount": (
                                    float(line_item.line_amount)
                                    if line_item.line_amount
                                    else 0.0
                                ),
                                "account_code": line_item.account_code,
                                "tax_type": line_item.tax_type,
                                "tax_amount": (
                                    float(line_item.tax_amount)
                                    if line_item.tax_amount
                                    else 0.0
                                ),
                            }
                            transaction_data["line_items"].append(line_data)

                    transactions_data.append(transaction_data)

            logger.info(f"Retrieved {len(transactions_data)} bank transactions")
            return transactions_data

        except ApiException as e:
            logger.error(f"Xero API error fetching bank transactions: {e}")
            raise
        except Exception as e:
            logger.error(f"Error fetching bank transactions: {e}")
            raise

    def get_invoices(
        self, from_date: Optional[datetime] = None, to_date: Optional[datetime] = None
    ) -> List[Dict[str, Any]]:
        """
        Get invoices from Xero.
        Maps to GET /Invoices endpoint.
        """
        try:
            if not to_date:
                to_date = datetime.now()
            if not from_date:
                from_date = to_date - timedelta(days=90)

            logger.info(
                f"Fetching invoices from {from_date.strftime('%Y-%m-%d')} to {to_date.strftime('%Y-%m-%d')}"
            )

            where_clause = f"Date >= DateTime({from_date.year}, {from_date.month}, {from_date.day}) AND Date <= DateTime({to_date.year}, {to_date.month}, {to_date.day})"

            invoices = self.accounting_api.get_invoices(
                xero_tenant_id=self.tenant_id, where=where_clause, order="Date DESC"
            )

            invoices_data = []
            if invoices.invoices:
                for invoice in invoices.invoices:
                    invoice_data = {
                        "invoice_id": invoice.invoice_id,
                        "invoice_number": invoice.invoice_number,
                        "type": invoice.type.value if invoice.type else None,
                        "status": invoice.status.value if invoice.status else None,
                        "date": invoice.date,
                        "due_date": invoice.due_date,
                        "total": float(invoice.total) if invoice.total else 0.0,
                        "amount_due": (
                            float(invoice.amount_due) if invoice.amount_due else 0.0
                        ),
                        "amount_paid": (
                            float(invoice.amount_paid) if invoice.amount_paid else 0.0
                        ),
                        "currency_code": (
                            invoice.currency_code.value
                            if invoice.currency_code
                            else "USD"
                        ),
                        "contact": {
                            "contact_id": (
                                invoice.contact.contact_id if invoice.contact else None
                            ),
                            "name": invoice.contact.name if invoice.contact else None,
                        },
                        "line_items": [],
                    }

                    # Process line items
                    if invoice.line_items:
                        for line_item in invoice.line_items:
                            line_data = {
                                "description": line_item.description,
                                "quantity": (
                                    float(line_item.quantity)
                                    if line_item.quantity
                                    else 1.0
                                ),
                                "unit_amount": (
                                    float(line_item.unit_amount)
                                    if line_item.unit_amount
                                    else 0.0
                                ),
                                "line_amount": (
                                    float(line_item.line_amount)
                                    if line_item.line_amount
                                    else 0.0
                                ),
                                "account_code": line_item.account_code,
                                "tax_type": line_item.tax_type,
                                "tax_amount": (
                                    float(line_item.tax_amount)
                                    if line_item.tax_amount
                                    else 0.0
                                ),
                            }
                            invoice_data["line_items"].append(line_data)

                    invoices_data.append(invoice_data)

            logger.info(f"Retrieved {len(invoices_data)} invoices")
            return invoices_data

        except ApiException as e:
            logger.error(f"Xero API error fetching invoices: {e}")
            raise
        except Exception as e:
            logger.error(f"Error fetching invoices: {e}")
            raise

    def get_contacts(self) -> List[Dict[str, Any]]:
        """
        Get contacts/customers from Xero.
        Maps to GET /Contacts endpoint.
        """
        try:
            logger.info("Fetching contacts from Xero")

            contacts = self.accounting_api.get_contacts(
                xero_tenant_id=self.tenant_id, where='ContactStatus=="ACTIVE"'
            )

            contacts_data = []
            if contacts.contacts:
                for contact in contacts.contacts:
                    contact_data = {
                        "contact_id": contact.contact_id,
                        "name": contact.name,
                        "contact_number": contact.contact_number,
                        "account_number": contact.account_number,
                        "contact_status": (
                            contact.contact_status.value
                            if contact.contact_status
                            else None
                        ),
                        "first_name": contact.first_name,
                        "last_name": contact.last_name,
                        "email_address": contact.email_address,
                        "is_supplier": contact.is_supplier,
                        "is_customer": contact.is_customer,
                        "default_currency": (
                            contact.default_currency.value
                            if contact.default_currency
                            else None
                        ),
                        "addresses": [],
                        "phones": [],
                    }

                    # Process addresses
                    if contact.addresses:
                        for address in contact.addresses:
                            address_data = {
                                "address_type": (
                                    address.address_type.value
                                    if address.address_type
                                    else None
                                ),
                                "address_line1": address.address_line1,
                                "address_line2": address.address_line2,
                                "city": address.city,
                                "region": address.region,
                                "postal_code": address.postal_code,
                                "country": address.country,
                            }
                            contact_data["addresses"].append(address_data)

                    # Process phone numbers
                    if contact.phones:
                        for phone in contact.phones:
                            phone_data = {
                                "phone_type": (
                                    phone.phone_type.value if phone.phone_type else None
                                ),
                                "phone_number": phone.phone_number,
                                "phone_area_code": phone.phone_area_code,
                                "phone_country_code": phone.phone_country_code,
                            }
                            contact_data["phones"].append(phone_data)

                    contacts_data.append(contact_data)

            logger.info(f"Retrieved {len(contacts_data)} contacts")
            return contacts_data

        except ApiException as e:
            logger.error(f"Xero API error fetching contacts: {e}")
            raise
        except Exception as e:
            logger.error(f"Error fetching contacts: {e}")
            raise
