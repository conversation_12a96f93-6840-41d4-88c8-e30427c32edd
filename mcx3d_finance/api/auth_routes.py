import logging
from fastapi import APIRouter, Request
from fastapi.responses import RedirectResponse
from ..auth import xero_oauth

logger = logging.getLogger(__name__)
router = APIRouter()


@router.get("/auth/xero/login")
async def xero_login():
    """
    Redirects the user to the Xero authorization URL.
    """
    auth_url = xero_oauth.generate_auth_url()
    if auth_url:
        return RedirectResponse(auth_url)
    return {"error": "Could not generate Xero auth URL"}, 500


@router.get("/auth/xero/callback")
async def xero_callback(request: Request):
    """
    Handles the callback from Xero after user authorization.
    """
    redirect_url = xero_oauth.handle_callback(str(request.url))
    if redirect_url:
        return RedirectResponse(redirect_url)
    return {"error": "An error occurred during the Xero callback."}, 500
