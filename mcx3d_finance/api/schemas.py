from pydantic import BaseModel
from typing import Dict, Any


class ReportParams(BaseModel):
    organization_id: int
    period: str


class ReportResponse(BaseModel):
    report_name: str
    data: Dict[str, Any]


class BalanceSheetResponse(BaseModel):
    report_name: str
    data: Dict[str, Any]


class CashFlowResponse(BaseModel):
    report_name: str
    data: Dict[str, Any]


class MetricsParams(BaseModel):
    organization_id: int
    period: str


class SaasKpisResponse(BaseModel):
    kpis: Dict[str, Any]
