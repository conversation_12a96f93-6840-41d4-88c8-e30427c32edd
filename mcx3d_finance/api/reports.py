from fastapi import APIRouter, Depends, Query
from sqlalchemy.orm import Session
from fastapi.responses import StreamingResponse
from mcx3d_finance.db.session import get_db
from mcx3d_finance.core.financials import income_statement, balance_sheet, cash_flow
from mcx3d_finance.core.valuation import dcf, multiples
from mcx3d_finance.reporting.generator import ReportGenerator
from mcx3d_finance.db import models
from sqlalchemy import func
from datetime import datetime

router = APIRouter()


@router.get("/reports/income-statement")
def get_income_statement(
    organization_id: int,
    start_date: str,
    end_date: str,
    format: str = Query("json", enum=["json", "pdf", "excel", "html"]),
    db: Session = Depends(get_db),
):
    start_date_obj = datetime.strptime(start_date, "%Y-%m-%d")
    end_date_obj = datetime.strptime(end_date, "%Y-%m-%d")

    transactions = (
        db.query(
            models.Account.type, func.sum(models.Transaction.amount).label("amount")
        )
        .join(models.Account)
        .filter(
            models.Transaction.organization_id == organization_id,
            models.Transaction.date >= start_date_obj,
            models.Transaction.date <= end_date_obj,
        )
        .group_by(models.Account.type)
        .all()
    )

    data = [{"account_type": t[0], "amount": t[1]} for t in transactions]

    if format == "json":
        return {
            "report_name": "Income Statement",
            "data": income_statement.calculate_income_statement(data),
        }

    report_generator = ReportGenerator(data, "income_statement", format)
    report_buffer = report_generator.generate()

    media_types = {
        "pdf": "application/pdf",
        "excel": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
        "html": "text/html",
    }
    headers = {
        "Content-Disposition": f'attachment; filename="income_statement.{format}"'
    }
    return StreamingResponse(
        report_buffer, media_type=media_types[format], headers=headers
    )


@router.get("/reports/balance-sheet")
def get_balance_sheet(
    organization_id: int,
    date: str,
    format: str = Query("json", enum=["json", "pdf", "excel", "html"]),
    db: Session = Depends(get_db),
):
    date_obj = datetime.strptime(date, "%Y-%m-%d")

    transactions = (
        db.query(
            models.Account.type, func.sum(models.Transaction.amount).label("amount")
        )
        .join(models.Account)
        .filter(
            models.Transaction.organization_id == organization_id,
            models.Transaction.date <= date_obj,
        )
        .group_by(models.Account.type)
        .all()
    )

    data = [{"account_type": t[0], "amount": t[1]} for t in transactions]
    if format == "json":
        return {
            "report_name": "Balance Sheet",
            "data": balance_sheet.calculate_balance_sheet(data),
        }

    report_generator = ReportGenerator(data, "balance_sheet", format)
    report_buffer = report_generator.generate()

    media_types = {
        "pdf": "application/pdf",
        "excel": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
        "html": "text/html",
    }
    headers = {"Content-Disposition": f'attachment; filename="balance_sheet.{format}"'}
    return StreamingResponse(
        report_buffer, media_type=media_types[format], headers=headers
    )


@router.get("/reports/cash-flow")
def get_cash_flow(
    organization_id: int,
    start_date: str,
    end_date: str,
    format: str = Query("json", enum=["json", "pdf", "excel", "html"]),
    db: Session = Depends(get_db),
):
    start_date_obj = datetime.strptime(start_date, "%Y-%m-%d")
    end_date_obj = datetime.strptime(end_date, "%Y-%m-%d")

    transactions = (
        db.query(
            models.Account.type, func.sum(models.Transaction.amount).label("amount")
        )
        .join(models.Account)
        .filter(
            models.Transaction.organization_id == organization_id,
            models.Transaction.date >= start_date_obj,
            models.Transaction.date <= end_date_obj,
        )
        .group_by(models.Account.type)
        .all()
    )

    data = [{"account_type": t[0], "amount": t[1]} for t in transactions]
    if format == "json":
        return {
            "report_name": "Cash Flow Statement",
            "data": cash_flow.calculate_cash_flow(data),
        }

    report_generator = ReportGenerator(data, "cash_flow", format)
    report_buffer = report_generator.generate()

    media_types = {
        "pdf": "application/pdf",
        "excel": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
        "html": "text/html",
    }
    headers = {"Content-Disposition": f'attachment; filename="cash_flow.{format}"'}
    return StreamingResponse(
        report_buffer, media_type=media_types[format], headers=headers
    )


@router.post("/valuation/dcf")
def get_dcf_valuation(
    financial_projections: list, discount_rate: float, terminal_growth_rate: float
):
    valuation = dcf.calculate_dcf_valuation(
        financial_projections, discount_rate, terminal_growth_rate
    )
    return {"valuation": valuation}


@router.post("/valuation/multiples")
def get_multiples_valuation(financial_metrics: dict, comparable_multiples: dict):
    valuation = multiples.calculate_multiples_valuation(
        financial_metrics, comparable_multiples
    )
    return {"valuation": valuation}
