#!/usr/bin/env python3
"""
Performance benchmarking suite for MCX3D Financial System Phase 1.
Tests performance with realistic data volumes and provides optimization recommendations.
"""

import sys
import os
import time
import psutil
import gc
from datetime import datetime, timedelta
from decimal import Decimal
import pandas as pd
from unittest.mock import Mock
from concurrent.futures import Thread<PERSON>oolExecutor, ProcessPoolExecutor
import threading

# Add project root to path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# Import Phase 1 components
from mcx3d_finance.core.financials.cash_flow import CashFlowGenerator
from mcx3d_finance.core.valuation.dcf import DCFValuation
from mcx3d_finance.core.valuation.multiples import MultiplesValuation
from mcx3d_finance.core.metrics.saas_kpis import SaaSKPICalculator


class PerformanceBenchmark:
    """Performance benchmarking suite for Phase 1 components."""
    
    def __init__(self):
        self.results = {}
        self.memory_usage = {}
        
    def measure_performance(self, func, *args, **kwargs):
        """Measure execution time and memory usage of a function."""
        # Force garbage collection before measurement
        gc.collect()
        
        # Get initial memory usage
        process = psutil.Process()
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        # Measure execution time
        start_time = time.time()
        result = func(*args, **kwargs)
        end_time = time.time()
        
        # Get final memory usage
        final_memory = process.memory_info().rss / 1024 / 1024  # MB
        memory_delta = final_memory - initial_memory
        
        execution_time = end_time - start_time
        
        return {
            'result': result,
            'execution_time': execution_time,
            'memory_used': memory_delta,
            'initial_memory': initial_memory,
            'final_memory': final_memory
        }
    
    def benchmark_cash_flow_generator(self):
        """Benchmark cash flow generator performance."""
        print("📊 Benchmarking Cash Flow Generator...")
        
        # Create mock session with realistic data volume
        mock_session = Mock()
        mock_org = Mock()
        mock_org.name = "Performance Test Corp"
        mock_org.id = 1
        mock_session.query().filter().first.return_value = mock_org
        
        # Simulate larger transaction volumes
        mock_session.query().join().filter().scalar.return_value = 1000000  # $1M transactions
        
        generator = CashFlowGenerator(mock_session)
        
        # Test different scenarios
        scenarios = [
            ("Small Dataset", datetime(2024, 10, 1), datetime(2024, 12, 31)),
            ("Medium Dataset", datetime(2024, 7, 1), datetime(2024, 12, 31)),
            ("Large Dataset", datetime(2024, 1, 1), datetime(2024, 12, 31)),
        ]
        
        results = {}
        
        for scenario_name, from_date, to_date in scenarios:
            print(f"  Testing {scenario_name}...")
            
            # Test indirect method
            indirect_perf = self.measure_performance(
                generator.generate_cash_flow_statement,
                organization_id=1,
                from_date=from_date,
                to_date=to_date,
                method="indirect",
                include_comparative=False
            )
            
            # Test direct method
            direct_perf = self.measure_performance(
                generator.generate_cash_flow_statement,
                organization_id=1,
                from_date=from_date,
                to_date=to_date,
                method="direct",
                include_comparative=False
            )
            
            results[scenario_name] = {
                'indirect': indirect_perf,
                'direct': direct_perf
            }
            
            print(f"    Indirect: {indirect_perf['execution_time']:.3f}s, {indirect_perf['memory_used']:.1f}MB")
            print(f"    Direct: {direct_perf['execution_time']:.3f}s, {direct_perf['memory_used']:.1f}MB")
        
        self.results['cash_flow'] = results
        return results
    
    def benchmark_dcf_valuation(self):
        """Benchmark DCF valuation performance."""
        print("\n💰 Benchmarking DCF Valuation...")
        
        dcf = DCFValuation()
        
        # Create projections of different sizes
        projection_scenarios = [
            ("5-Year", 5),
            ("10-Year", 10),
            ("15-Year", 15),
        ]
        
        results = {}
        
        for scenario_name, years in projection_scenarios:
            print(f"  Testing {scenario_name} Projections...")
            
            # Generate projections
            projections = []
            for year in range(1, years + 1):
                projections.append({
                    "year": year,
                    "revenue": 1000000 * (1.2 ** year),
                    "free_cash_flow": 300000 * (1.15 ** year),
                    "ebitda": 400000 * (1.18 ** year),
                })
            
            # Test basic DCF
            dcf_perf = self.measure_performance(
                dcf.calculate_dcf_valuation,
                financial_projections=projections,
                discount_rate=0.12,
                terminal_growth_rate=0.03,
                scenarios=["base", "upside", "downside"]
            )
            
            # Test Monte Carlo (reduced simulations for benchmarking)
            mc_perf = self.measure_performance(
                dcf.perform_monte_carlo_simulation,
                projections=projections,
                discount_rate=0.12,
                terminal_growth_rate=0.03,
                num_simulations=1000  # Reduced for benchmarking
            )
            
            results[scenario_name] = {
                'dcf': dcf_perf,
                'monte_carlo': mc_perf
            }
            
            print(f"    DCF: {dcf_perf['execution_time']:.3f}s, {dcf_perf['memory_used']:.1f}MB")
            print(f"    Monte Carlo: {mc_perf['execution_time']:.3f}s, {mc_perf['memory_used']:.1f}MB")
        
        self.results['dcf'] = results
        return results
    
    def benchmark_multiples_valuation(self):
        """Benchmark multiples valuation performance."""
        print("\n📈 Benchmarking Multiples Valuation...")
        
        multiples = MultiplesValuation()
        
        target_metrics = {
            "revenue": 10000000,
            "ebitda": 3000000,
            "net_income": 1500000,
            "free_cash_flow": 2000000,
        }
        
        # Test with different numbers of comparable companies
        comp_scenarios = [
            ("Small Peer Group", 5),
            ("Medium Peer Group", 15),
            ("Large Peer Group", 50),
        ]
        
        results = {}
        
        for scenario_name, num_comps in comp_scenarios:
            print(f"  Testing {scenario_name} ({num_comps} companies)...")
            
            # Generate comparable companies
            comparable_companies = []
            for i in range(num_comps):
                comparable_companies.append({
                    "company": f"Comp {i+1}",
                    "ev_revenue": 3.0 + (i * 0.1),
                    "ev_ebitda": 12.0 + (i * 0.5),
                    "pe_ratio": 20.0 + (i * 0.8),
                    "price_to_book": 2.0 + (i * 0.1),
                })
            
            # Test multiples valuation
            multiples_perf = self.measure_performance(
                multiples.calculate_comprehensive_multiples_valuation,
                target_metrics=target_metrics,
                comparable_companies=comparable_companies,
                valuation_multiples=["ev_revenue", "ev_ebitda", "pe_ratio"]
            )
            
            results[scenario_name] = multiples_perf
            
            print(f"    Multiples: {multiples_perf['execution_time']:.3f}s, {multiples_perf['memory_used']:.1f}MB")
        
        self.results['multiples'] = results
        return results
    
    def benchmark_saas_kpis(self):
        """Benchmark SaaS KPIs calculator performance."""
        print("\n🚀 Benchmarking SaaS KPIs Calculator...")
        
        calculator = SaaSKPICalculator()
        
        # Test different time periods
        period_scenarios = [
            ("1 Month", datetime(2024, 12, 1), datetime(2024, 12, 31)),
            ("1 Quarter", datetime(2024, 10, 1), datetime(2024, 12, 31)),
            ("1 Year", datetime(2024, 1, 1), datetime(2024, 12, 31)),
        ]
        
        results = {}
        
        for scenario_name, from_date, to_date in period_scenarios:
            print(f"  Testing {scenario_name}...")
            
            # Test KPI calculation
            kpi_perf = self.measure_performance(
                calculator.calculate_comprehensive_kpis,
                organization_id=1,
                period_start=from_date,
                period_end=to_date
            )
            
            results[scenario_name] = kpi_perf
            
            print(f"    KPIs: {kpi_perf['execution_time']:.3f}s, {kpi_perf['memory_used']:.1f}MB")
        
        self.results['saas_kpis'] = results
        return results
    
    def benchmark_concurrent_operations(self):
        """Benchmark concurrent operations performance."""
        print("\n🔄 Benchmarking Concurrent Operations...")
        
        # Create instances
        mock_session = Mock()
        mock_org = Mock()
        mock_org.name = "Concurrent Test Corp"
        mock_org.id = 1
        mock_session.query().filter().first.return_value = mock_org
        mock_session.query().join().filter().scalar.return_value = 500000
        
        generator = CashFlowGenerator(mock_session)
        dcf = DCFValuation()
        calculator = SaaSKPICalculator()
        
        def run_cash_flow():
            return generator.generate_cash_flow_statement(
                organization_id=1,
                from_date=datetime(2024, 1, 1),
                to_date=datetime(2024, 12, 31),
                method="indirect"
            )
        
        def run_dcf():
            projections = [
                {"year": i, "revenue": 1000000 * i, "free_cash_flow": 300000 * i}
                for i in range(1, 6)
            ]
            return dcf.calculate_dcf_valuation(
                projections, 0.12, 0.03, ["base"]
            )
        
        def run_kpis():
            return calculator.calculate_comprehensive_kpis(
                organization_id=1,
                period_start=datetime(2024, 1, 1),
                period_end=datetime(2024, 12, 31)
            )
        
        # Test sequential execution
        print("  Testing Sequential Execution...")
        sequential_start = time.time()
        run_cash_flow()
        run_dcf()
        run_kpis()
        sequential_time = time.time() - sequential_start
        
        # Test concurrent execution
        print("  Testing Concurrent Execution...")
        concurrent_start = time.time()
        with ThreadPoolExecutor(max_workers=3) as executor:
            futures = [
                executor.submit(run_cash_flow),
                executor.submit(run_dcf),
                executor.submit(run_kpis)
            ]
            # Wait for all to complete
            for future in futures:
                future.result()
        concurrent_time = time.time() - concurrent_start
        
        speedup = sequential_time / concurrent_time
        
        results = {
            'sequential_time': sequential_time,
            'concurrent_time': concurrent_time,
            'speedup': speedup
        }
        
        print(f"    Sequential: {sequential_time:.3f}s")
        print(f"    Concurrent: {concurrent_time:.3f}s")
        print(f"    Speedup: {speedup:.2f}x")
        
        self.results['concurrency'] = results
        return results
    
    def generate_performance_report(self):
        """Generate comprehensive performance report."""
        print("\n" + "=" * 60)
        print("📋 PERFORMANCE BENCHMARK REPORT")
        print("=" * 60)
        
        # System information
        print(f"System: {psutil.cpu_count()} CPU cores, {psutil.virtual_memory().total / 1024**3:.1f}GB RAM")
        print(f"Python: {sys.version.split()[0]}")
        print(f"Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        # Performance summary
        print(f"\n📊 Performance Summary:")
        
        # Cash Flow Performance
        if 'cash_flow' in self.results:
            cf_results = self.results['cash_flow']
            avg_time = sum(
                cf_results[scenario]['indirect']['execution_time'] 
                for scenario in cf_results
            ) / len(cf_results)
            print(f"Cash Flow Generator: {avg_time:.3f}s average")
        
        # DCF Performance
        if 'dcf' in self.results:
            dcf_results = self.results['dcf']
            avg_time = sum(
                dcf_results[scenario]['dcf']['execution_time'] 
                for scenario in dcf_results
            ) / len(dcf_results)
            print(f"DCF Valuation: {avg_time:.3f}s average")
        
        # Multiples Performance
        if 'multiples' in self.results:
            mult_results = self.results['multiples']
            avg_time = sum(
                mult_results[scenario]['execution_time'] 
                for scenario in mult_results
            ) / len(mult_results)
            print(f"Multiples Valuation: {avg_time:.3f}s average")
        
        # SaaS KPIs Performance
        if 'saas_kpis' in self.results:
            kpi_results = self.results['saas_kpis']
            avg_time = sum(
                kpi_results[scenario]['execution_time'] 
                for scenario in kpi_results
            ) / len(kpi_results)
            print(f"SaaS KPIs Calculator: {avg_time:.3f}s average")
        
        # Concurrency Performance
        if 'concurrency' in self.results:
            conc_results = self.results['concurrency']
            print(f"Concurrent Speedup: {conc_results['speedup']:.2f}x")
        
        # Performance Assessment
        print(f"\n🎯 Performance Assessment:")
        
        # Check if performance meets requirements
        requirements_met = True
        
        # Requirement: Generate reports in <5 seconds
        max_times = []
        for component in ['cash_flow', 'dcf', 'multiples', 'saas_kpis']:
            if component in self.results:
                if component == 'cash_flow':
                    max_time = max(
                        self.results[component][scenario]['indirect']['execution_time']
                        for scenario in self.results[component]
                    )
                elif component == 'dcf':
                    max_time = max(
                        self.results[component][scenario]['dcf']['execution_time']
                        for scenario in self.results[component]
                    )
                else:
                    max_time = max(
                        self.results[component][scenario]['execution_time']
                        for scenario in self.results[component]
                    )
                max_times.append(max_time)
        
        if max_times:
            overall_max_time = max(max_times)
            if overall_max_time < 5.0:
                print("✅ All components meet <5 second requirement")
            else:
                print(f"⚠️  Some components exceed 5 second requirement (max: {overall_max_time:.2f}s)")
                requirements_met = False
        
        # Memory usage assessment
        total_memory_used = 0
        for component_results in self.results.values():
            if isinstance(component_results, dict):
                for scenario_result in component_results.values():
                    if isinstance(scenario_result, dict) and 'memory_used' in scenario_result:
                        total_memory_used += scenario_result['memory_used']
        
        if total_memory_used < 500:  # 500MB threshold
            print(f"✅ Memory usage within acceptable limits ({total_memory_used:.1f}MB total)")
        else:
            print(f"⚠️  High memory usage detected ({total_memory_used:.1f}MB total)")
        
        # Optimization recommendations
        print(f"\n💡 Optimization Recommendations:")
        
        if overall_max_time > 2.0:
            print("• Consider implementing caching for frequently accessed calculations")
            print("• Optimize database queries with proper indexing")
        
        if total_memory_used > 200:
            print("• Consider processing large datasets in chunks")
            print("• Implement memory-efficient data structures")
        
        if 'concurrency' in self.results and self.results['concurrency']['speedup'] < 2.0:
            print("• Optimize for better concurrent performance")
            print("• Consider async/await patterns for I/O operations")
        
        print("• Use connection pooling for database operations")
        print("• Implement Redis caching for expensive calculations")
        
        return {
            'requirements_met': requirements_met,
            'max_execution_time': overall_max_time if max_times else 0,
            'total_memory_used': total_memory_used,
            'concurrent_speedup': self.results.get('concurrency', {}).get('speedup', 1.0)
        }


def run_performance_benchmarks():
    """Run all performance benchmarks."""
    print("⚡ MCX3D Financial System - Phase 1 Performance Benchmarks")
    print("=" * 60)
    print(f"Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    benchmark = PerformanceBenchmark()
    
    try:
        # Run all benchmarks
        benchmark.benchmark_cash_flow_generator()
        benchmark.benchmark_dcf_valuation()
        benchmark.benchmark_multiples_valuation()
        benchmark.benchmark_saas_kpis()
        benchmark.benchmark_concurrent_operations()
        
        # Generate report
        report = benchmark.generate_performance_report()
        
        print(f"\nCompleted at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        if report['requirements_met']:
            print("\n🎉 ALL PERFORMANCE REQUIREMENTS MET!")
        else:
            print("\n⚠️  Some performance requirements not met - see recommendations above")
        
        return report
        
    except Exception as e:
        print(f"\n❌ Benchmark execution failed: {e}")
        import traceback
        traceback.print_exc()
        return None


if __name__ == "__main__":
    run_performance_benchmarks()
