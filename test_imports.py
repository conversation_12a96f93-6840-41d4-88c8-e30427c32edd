#!/usr/bin/env python3
"""
Test script to verify all imports are working correctly.
"""

def test_imports():
    """Test all critical imports for the MCX3D Financial system."""
    
    try:
        # Test core financial modules
        from mcx3d_finance.core.financials.income_statement import IncomeStatementGenerator
        print("✅ IncomeStatementGenerator import successful")
        
        from mcx3d_finance.core.financials.balance_sheet import BalanceSheetGenerator
        print("✅ BalanceSheetGenerator import successful")
        
        from mcx3d_finance.core.financials.cash_flow import CashFlowGenerator
        print("✅ CashFlowGenerator import successful")
        
        # Test valuation modules
        from mcx3d_finance.core.valuation.dcf import DCFValuation
        print("✅ DCFValuation import successful")
        
        from mcx3d_finance.core.valuation.multiples import MultiplesValuation
        print("✅ MultiplesValuation import successful")
        
        # Test SaaS KPIs
        from mcx3d_finance.core.metrics.saas_kpis import SaaSKPICalculator
        print("✅ SaaSKPICalculator import successful")
        
        # Test Xero integration
        from mcx3d_finance.integrations.xero_client import XeroClient
        print("✅ XeroClient import successful")
        
        from mcx3d_finance.auth.xero_oauth import XeroAuthManager
        print("✅ XeroAuthManager import successful")
        
        # Test database models
        from mcx3d_finance.db.models import Organization, Account, Contact, Transaction
        print("✅ Database models import successful")
        
        # Test data processors
        from mcx3d_finance.core.data_processors import XeroDataProcessor
        print("✅ XeroDataProcessor import successful")
        
        print("\n🎉 All imports successful! The system is ready.")
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

if __name__ == "__main__":
    test_imports()
