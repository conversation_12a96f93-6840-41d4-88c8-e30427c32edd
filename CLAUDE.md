# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

### Docker Development Environment
The project uses Docker Compose for local development. Key commands:

```bash
# Start all services (Fast<PERSON><PERSON>, PostgreSQL, Redis, Celery worker)
docker-compose up --build

# Run commands inside web container
docker-compose exec web <command>

# Stop services
docker-compose down
```

### Testing
```bash
# Run all tests
docker-compose exec web pytest

# Run specific test categories
docker-compose exec web pytest -m e2e  # End-to-end tests
docker-compose exec web pytest tests/core/  # Core functionality tests
docker-compose exec web pytest tests/integration/  # Integration tests

# Run tests with coverage
docker-compose exec web pytest --cov=mcx3d_finance
```

### Code Quality
```bash
# Format code with Black
docker-compose exec web black mcx3d_finance/

# Lint with flake8
docker-compose exec web flake8 mcx3d_finance/
```

### CLI Usage
The project includes a command-line interface accessible via Python module:

```bash
# Generate financial reports
docker-compose exec web python -m mcx3d_finance.cli.main generate income-statement --organization-id <id> --period <period> --format <pdf|excel|html>
docker-compose exec web python -m mcx3d_finance.cli.main generate balance-sheet --organization-id <id> --date <date> --format <pdf|excel|html>

# Run valuations
docker-compose exec web python -m mcx3d_finance.cli.main valuate dcf --config <config.json>
docker-compose exec web python -m mcx3d_finance.cli.main valuate multiples --config <config.json>

# Sync data from Xero
docker-compose exec web python -m mcx3d_finance.cli.main sync xero --org-id <id> [--incremental]
```

## Architecture Overview

### Core Structure
- **mcx3d_finance/**: Main application package
  - **api/**: FastAPI REST endpoints with NASDAQ compliance features
  - **core/**: Business logic and financial calculations
    - **financials/**: Financial statement generators (balance sheet, income statement, cash flow)
    - **metrics/**: SaaS KPI calculations
    - **valuation/**: DCF and multiples-based valuation models
  - **db/**: Database models and session management
  - **integrations/**: External API integrations (Xero)
  - **tasks/**: Celery background tasks
  - **cli/**: Command-line interface
  - **reporting/**: Report generation (PDF, Excel)

### Key Components

#### Financial Statement Generation
- **BalanceSheetGenerator**: Generates NASDAQ-compliant balance sheets
- **IncomeStatementGenerator**: Creates income statements with earnings calculations
- **CashFlowGenerator**: Produces cash flow statements

#### Valuation Models
- **DCF**: Discounted Cash Flow modeling with configurable assumptions
- **Multiples**: Industry multiples-based valuation

#### External Integrations
- **Xero OAuth**: Authentication and token management
- **Xero Client**: Data synchronization and API calls

### Database & Infrastructure
- **PostgreSQL**: Primary database with SQLAlchemy ORM
- **Redis**: Caching and Celery task queue
- **Celery**: Background task processing
- **Alembic**: Database migrations

### API Architecture
FastAPI application with:
- **Authentication**: Xero OAuth integration
- **Webhooks**: Real-time data synchronization
- **Reports**: Financial statement generation endpoints
- **Metrics**: SaaS KPI calculation endpoints

## Configuration

### Environment Variables
- `DATABASE_URL`: PostgreSQL connection string
- `REDIS_URL`: Redis connection string
- `XERO_CLIENT_ID`: Xero OAuth client ID
- `XERO_CLIENT_SECRET`: Xero OAuth client secret
- `XERO_WEBHOOK_KEY`: Xero webhook verification key

### Configuration Files
- `config.yml`: Application configuration
- `alembic.ini`: Database migration configuration
- `pytest.ini`: Test configuration with e2e marker
- `pyproject.toml`: Black formatter configuration (88 char line length)

## Development Notes

### Testing Strategy
- **Unit Tests**: Core business logic in `tests/core/`
- **Integration Tests**: Database and external API interactions in `tests/integration/`
- **E2E Tests**: Full API and CLI workflows in `tests/e2e/`

### Code Style
- Black formatter with 88 character line length
- flake8 linting
- SQLAlchemy ORM patterns
- FastAPI async/await patterns

### Data Flow
1. **Xero Integration**: OAuth → Data Sync → Database Storage
2. **Financial Processing**: Raw Data → Financial Calculations → Report Generation
3. **Valuation**: Historical Data → DCF/Multiples Models → Valuation Results
4. **API/CLI**: User Request → Business Logic → Response/File Generation

### Background Tasks
Celery handles:
- Large report generation (PDF/Excel)
- Data synchronization from Xero
- Valuation calculations
- Batch processing operations